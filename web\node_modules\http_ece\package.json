{"name": "http_ece", "version": "1.2.0", "description": "Encrypted Content-Encoding for HTTP", "homepage": "https://github.com/martinthomson/encrypted-content-encoding", "bugs": "https://github.com/martinthomson/encrypted-content-encoding/issues", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/martinthomson/encrypted-content-encoding.git"}, "license": "MIT", "main": "./ece.js", "scripts": {"test": "node ./test.js"}, "engines": {"node": ">=16"}}