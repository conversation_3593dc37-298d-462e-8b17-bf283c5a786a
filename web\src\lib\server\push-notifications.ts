import webpush from 'web-push';
import { prisma } from '$lib/server/prisma';
import { dev } from '$app/environment';

// VAPID keys - these should be generated and stored securely
const VAPID_PUBLIC_KEY =
  process.env.VAPID_PUBLIC_KEY || 'BEl62iUYgUivxIkv69yViEuiBIa40HdHSWgNfZeeyCLdmw';
const VAPID_PRIVATE_KEY = process.env.VAPID_PRIVATE_KEY || 'your-private-key-here';
const VAPID_EMAIL = process.env.VAPID_EMAIL || 'mailto:<EMAIL>';

// Configure web-push
if (!dev && VAPID_PRIVATE_KEY !== 'your-private-key-here') {
  webpush.setVapidDetails(VAPID_EMAIL, VAPID_PUBLIC_KEY, VAPID_PRIVATE_KEY);
}

export interface PushNotificationData {
  title: string;
  message: string;
  url?: string;
  icon?: string;
  badge?: string;
  data?: Record<string, any>;
}

/**
 * Send push notification to a specific user
 */
export async function sendPushNotificationToUser(
  userId: string,
  notificationData: PushNotificationData
): Promise<boolean> {
  try {
    // Check if user has push notifications enabled
    const notificationSettings = await prisma.notificationSettings.findUnique({
      where: { userId },
    });

    if (!notificationSettings?.pushEnabled) {
      console.log(`User ${userId} has push notifications disabled`);
      return false;
    }

    // Get user's push subscriptions
    const subscriptions = await prisma.pushSubscription.findMany({
      where: { userId },
    });

    if (subscriptions.length === 0) {
      console.log(`No push subscriptions found for user ${userId}`);
      return false;
    }

    // Prepare push payload
    const payload = JSON.stringify({
      title: notificationData.title,
      message: notificationData.message,
      body: notificationData.message, // For compatibility
      url: notificationData.url || '/dashboard/notifications',
      icon: notificationData.icon || '/favicon.ico',
      badge: notificationData.badge || '/favicon.ico',
      data: notificationData.data || {},
    });

    // Send to all user's subscriptions
    const results = await Promise.allSettled(
      subscriptions.map(async (subscription) => {
        try {
          const pushSubscription = {
            endpoint: subscription.endpoint,
            keys: {
              p256dh: subscription.p256dh || '',
              auth: subscription.auth || '',
            },
          };

          if (dev) {
            console.log(`[DEV] Would send push notification to ${userId}:`, payload);
            return { success: true };
          }

          const result = await webpush.sendNotification(pushSubscription, payload);
          console.log(`Push notification sent to user ${userId}:`, result.statusCode);
          return { success: true };
        } catch (error: any) {
          console.error(
            `Failed to send push notification to subscription ${subscription.id}:`,
            error
          );

          // If subscription is invalid, remove it
          if (error.statusCode === 410 || error.statusCode === 404) {
            console.log(`Removing invalid subscription ${subscription.id}`);
            await prisma.pushSubscription.delete({
              where: { id: subscription.id },
            });
          }

          return { success: false, error };
        }
      })
    );

    // Check if at least one notification was sent successfully
    const successCount = results.filter(
      (result) => result.status === 'fulfilled' && result.value.success
    ).length;

    console.log(
      `Push notifications sent: ${successCount}/${subscriptions.length} for user ${userId}`
    );
    return successCount > 0;
  } catch (error) {
    console.error('Error sending push notification:', error);
    return false;
  }
}

/**
 * Send push notification to multiple users
 */
export async function sendPushNotificationToUsers(
  userIds: string[],
  notificationData: PushNotificationData
): Promise<{ success: number; failed: number }> {
  const results = await Promise.allSettled(
    userIds.map((userId) => sendPushNotificationToUser(userId, notificationData))
  );

  const success = results.filter((result) => result.status === 'fulfilled' && result.value).length;
  const failed = results.length - success;

  console.log(`Bulk push notifications: ${success} successful, ${failed} failed`);
  return { success, failed };
}

/**
 * Test push notification functionality
 */
export async function sendTestPushNotification(userId: string): Promise<boolean> {
  return sendPushNotificationToUser(userId, {
    title: 'Test Notification',
    message: 'This is a test push notification from Auto Apply',
    url: '/dashboard/notifications',
    data: { test: true },
  });
}

/**
 * Get VAPID public key for client-side subscription
 */
export function getVapidPublicKey(): string {
  return VAPID_PUBLIC_KEY;
}
