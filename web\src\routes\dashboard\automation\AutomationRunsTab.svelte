<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input/index.js';
  import * as Card from '$lib/components/ui/card';
  import * as Select from '$lib/components/ui/select';
  import { Badge } from '$lib/components/ui/badge';
  import { Progress } from '$lib/components/ui/progress/index.js';
  import FeatureGuard from '$components/features/EnhancedFeatureGuard.svelte';
  import { getProfileData } from '$lib/utils/profile';
  import { formatDistance } from 'date-fns';
  import ResolvedKeywords from '$lib/components/automation/ResolvedKeywords.svelte';
  import ResolvedLocations from '$lib/components/automation/ResolvedLocations.svelte';
  import {
    Search,
    Play,
    ExternalLink,
    CheckCircle,
    Clock,
    XCircle,
    StopCircle,
  } from 'lucide-svelte';
  import { goto } from '$app/navigation';

  const { userData, automationRuns, onRunSelect, onCreateRun } = $props<{
    userData: any;
    automationRuns: any;
    onRunSelect: (run: any) => void;
    onCreateRun: () => void;
  }>();

  // Filter and search state
  let runStatusFilter = $state('all');
  let runSearchQuery = $state('');

  // Status filter options
  const statusFilterOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'pending', label: 'Pending' },
    { value: 'start', label: 'Starting' },
    { value: 'in progress', label: 'In Progress' },
    { value: 'running', label: 'Running' },
    { value: 'completed', label: 'Completed' },
    { value: 'failed', label: 'Failed' },
    { value: 'stopped', label: 'Stopped' },
  ];

  // Get current status filter option
  const currentStatusOption = $derived(() => {
    return (
      statusFilterOptions.find((option) => option.value === runStatusFilter) ||
      statusFilterOptions[0]
    );
  });

  // Filtered automation runs
  const filteredAutomationRuns = $derived(() => {
    return $automationRuns.filter((run) => {
      // Status filter
      if (runStatusFilter !== 'all' && run.status !== runStatusFilter) {
        return false;
      }

      // Search filter
      if (runSearchQuery.trim()) {
        const query = runSearchQuery.toLowerCase();
        const profileName = run.profile ? getProfileData(run.profile).fullName || '' : '';
        const keywords = run.keywords || '';
        const location = run.location || '';

        return (
          profileName.toLowerCase().includes(query) ||
          keywords.toLowerCase().includes(query) ||
          location.toLowerCase().includes(query)
        );
      }

      return true;
    });
  });

  // Helper functions
  function formatDistanceToNow(date: Date | string): string {
    if (!date) return '';
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return formatDistance(dateObj, new Date(), { addSuffix: true });
  }

  function getStatusBadgeVariant(status: string) {
    switch (status) {
      case 'completed':
        return 'default';
      case 'start':
      case 'running':
        return 'secondary';
      case 'failed':
        return 'destructive';
      case 'stopped':
        return 'outline';
      case 'in progress':
      case 'pending':
        return 'outline';
      default:
        return 'outline';
    }
  }

  function getStatusIcon(status: string) {
    switch (status) {
      case 'completed':
        return CheckCircle;
      case 'start':
      case 'running':
        return Play;
      case 'failed':
        return XCircle;
      case 'stopped':
        return StopCircle;
      case 'in progress':
      case 'pending':
        return Clock;
      default:
        return Clock;
    }
  }

  function calculateProgress(run: any): number {
    if (run.status === 'completed') return 100;
    if (run.status === 'failed' || run.status === 'stopped') return run.progress || 0;
    if (run.status === 'start') return 5;
    if (run.status === 'in progress') return run.progress || 50;
    if (run.status === 'running') return run.progress || 50;
    return run.progress || 0;
  }

  function getStatusLabel(status: string): string {
    switch (status) {
      case 'start':
        return 'Starting';
      case 'in progress':
        return 'In Progress';
      case 'running':
        return 'Running';
      case 'completed':
        return 'Completed';
      case 'failed':
        return 'Failed';
      case 'stopped':
        return 'Stopped';
      case 'pending':
        return 'Pending';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  }
</script>

<FeatureGuard
  {userData}
  featureId="automation"
  limitId="automation_runs_per_month"
  showUpgradePrompt={true}
  fallbackMessage="Automation features are not available in your current plan">
  <div class="mb-6 flex flex-wrap items-center justify-between gap-4">
    <div class="flex flex-wrap items-center gap-2">
      <Select.Root
        type="single"
        value={runStatusFilter}
        onValueChange={(value) => {
          runStatusFilter = value || 'all';
        }}>
        <Select.Trigger class="w-[140px] p-2">
          <Select.Value placeholder={currentStatusOption().label} />
        </Select.Trigger>
        <Select.Content class="w-[140px]">
          {#each statusFilterOptions as option (option.value)}
            <Select.Item value={option.value}>
              {option.label}
            </Select.Item>
          {/each}
        </Select.Content>
      </Select.Root>
    </div>
    <div class="relative flex items-center gap-2">
      <Search class="text-muted-foreground absolute left-2.5 top-3 h-4 w-4" />
      <Input placeholder="Search runs..." class="h-10 w-[200px] pl-9" bind:value={runSearchQuery} />
    </div>
  </div>

  {#if $automationRuns.length === 0}
    <div
      class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center">
      <Search class="mb-4 h-12 w-12 text-gray-400" />
      <h3 class="text-xl font-semibold text-gray-300">No automation runs yet</h3>
      <p class="mt-2 text-gray-400">Create your first automation run to start searching for jobs</p>
      <Button variant="default" onclick={onCreateRun} class="mt-4">
        <Play class="mr-2 h-4 w-4" />
        New Automation Run
      </Button>
    </div>
  {:else if filteredAutomationRuns().length === 0}
    <div
      class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center">
      <Search class="mb-4 h-12 w-12 text-gray-400" />
      <h3 class="text-xl font-semibold text-gray-300">No runs match your filters</h3>
      <p class="mt-2 text-gray-400">Try adjusting your search or filter criteria</p>
    </div>
  {:else}
    <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {#each filteredAutomationRuns() as run (run.id)}
        <Card.Root class="gap-0 overflow-hidden p-0">
          <Card.Header class="border-border border-b !p-4">
            <div class="flex items-center justify-between">
              <Card.Title>
                {#if run.profile}
                  {getProfileData(run.profile).fullName || 'Unnamed Profile'}
                {:else}
                  Automation Run
                {/if}
              </Card.Title>
              <Badge variant={getStatusBadgeVariant(run.status)} class="ml-2">
                {#if getStatusIcon(run.status)}
                  {@const Icon = getStatusIcon(run.status)}
                  <Icon class="mr-1 h-3 w-3" />
                {/if}
                {getStatusLabel(run.status)}
              </Badge>
            </div>
            <Card.Description>
              {#if run.createdAt}
                Started {formatDistanceToNow(new Date(run.createdAt))} ago
              {/if}
            </Card.Description>
          </Card.Header>

          <Progress value={calculateProgress(run)} max={100} class="rounded-none" />
          <Card.Content class="flex flex-col gap-4 p-4 pt-3">
            <div class="flex flex-row justify-between text-xs">
              <div class="text-primary/50">Progress</div>
              <div class="text-primary/50 text-right">
                {calculateProgress(run)}% Complete
              </div>
            </div>

            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div class="font-medium text-gray-400">Keywords</div>
                <div class="truncate">
                  <ResolvedKeywords keywordIds={run.keywords || ''} fallback="None" />
                </div>
              </div>
              <div>
                <div class="font-medium text-gray-400">Location</div>
                <div class="truncate">
                  <ResolvedLocations locationIds={run.location || ''} fallback="None" />
                </div>
              </div>
            </div>

            <div class="flex flex-col">
              <div class="font-medium text-gray-400">Jobs Found</div>
              <div class="flex items-center gap-2">
                <span class="text-lg font-semibold"
                  >{run.matchedJobIds?.length || run.jobsFound || 0}</span>
                {#if ['running', 'pending', 'start', 'in progress'].includes(run.status)}
                  <span class="text-xs text-gray-400">(in progress)</span>
                {/if}
              </div>
            </div>
          </Card.Content>

          <Card.Footer class="grid grid-cols-2 gap-4 border-t !p-2">
            <Button variant="outline" size="sm" onclick={() => onRunSelect(run)}>
              View Details
            </Button>
            <Button
              variant="outline"
              size="sm"
              onclick={() => goto(`/dashboard/automation/${run.id}`)}>
              Full View
            </Button>
          </Card.Footer>
        </Card.Root>
      {/each}
    </div>
  {/if}
</FeatureGuard>
