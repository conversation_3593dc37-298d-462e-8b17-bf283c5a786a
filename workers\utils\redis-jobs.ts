// workers/utils/redis-jobs.ts
import { redis, createPrefixer } from "../redis.js";
import { prisma } from "./prisma.js";

// Job types
export enum JobType {
  RESUME_OPTIMIZATION = "resume-optimization",
  SEARCH = "search",
  JOB_MATCHING = "job-matching",
  RESUME_PARSING = "resume-parsing::stream",
  ATS_ANALYSIS = "ats-analysis",
  JOB_SPECIFIC_ANALYSIS = "job-specific-analysis",
  AUTOMATION = "automation::stream",
}

// Job status
export enum JobStatus {
  PENDING = "pending",
  PROCESSING = "processing",
  COMPLETED = "completed",
  FAILED = "failed",
}

// Stream configuration
const STREAM_PREFIX = "stream";
const GROUP_PREFIX = "group";
const CONSUMER_PREFIX = "consumer";
const STATUS_CHANNEL_PREFIX = "status";

// Create prefixed keys for different job types
export function getJobKeys(jobType: JobType) {
  // Special case for resume parsing which already includes "::stream" in its name
  if (jobType === JobType.RESUME_PARSING) {
    return {
      streamName: jobType, // Use the exact name without adding another "stream" suffix
      groupName: "resume-parsing::group",
      consumerName: "resume-parsing-consumer",
      statusChannel: "resume-parsing::status",
      prefix: (str: string) => `resume-parsing::${str}`,
    };
  }

  // Special case for automation which already includes "::stream" in its name
  if (jobType === JobType.AUTOMATION) {
    return {
      streamName: jobType, // Use the exact name without adding another "stream" suffix
      groupName: "automation::group",
      consumerName: "automation-consumer",
      statusChannel: "automation::status",
      prefix: (str: string) => `automation::${str}`,
    };
  }

  // Normal case for other job types
  const namespace = `${jobType}:`;
  const prefix = createPrefixer(namespace);

  return {
    streamName: prefix(STREAM_PREFIX),
    groupName: prefix(GROUP_PREFIX),
    consumerName: prefix(CONSUMER_PREFIX),
    statusChannel: prefix(STATUS_CHANNEL_PREFIX),
    prefix,
  };
}

// Create a stream group for a job type
export async function ensureJobGroup(jobType: JobType) {
  const { streamName, groupName } = getJobKeys(jobType);

  try {
    await redis.xgroup("CREATE", streamName, groupName, "$", "MKSTREAM");
    console.log(`[redis] Stream group '${groupName}' created for ${jobType}.`);
  } catch (err: any) {
    if (!err.message.includes("BUSYGROUP")) {
      console.error(
        `[redis] Failed to create stream group for ${jobType}:`,
        err
      );
    }
  }
}

// Add a job to a stream
export async function addJob(jobType: JobType, jobData: any) {
  const { streamName } = getJobKeys(jobType);

  try {
    // Generate a unique job ID
    const jobId = generateUniqueId();

    // Store job in database for persistence
    try {
      await prisma.workerProcess.create({
        data: {
          id: jobId,
          type: jobType,
          status: JobStatus.PENDING,
          data: jobData,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });
      console.log(`[db] Created WorkerProcess record for job ${jobId}`);
    } catch (dbErr) {
      console.error(`[db] Failed to create WorkerProcess record:`, dbErr);
      // Continue even if database operation fails
    }

    // Add job to Redis stream
    await redis.xadd(
      streamName,
      "*",
      "job",
      JSON.stringify({ ...jobData, jobId })
    );

    console.log(`[redis] Added job ${jobId} to ${jobType} stream.`);
    return jobId;
  } catch (err) {
    console.error(`[redis] Failed to add job to ${jobType} stream:`, err);
    throw err;
  }
}

// Generate a unique ID (UUID-like)
function generateUniqueId() {
  return (
    "job_" +
    Date.now().toString(36) +
    Math.random().toString(36).substring(2, 11)
  );
}

// Process jobs from a Redis stream
export async function processJobs(
  jobType: JobType,
  handler: (job: any) => Promise<void>,
  options = { count: 10, blockMs: 5000 }
) {
  // Ensure stream group exists
  await ensureJobGroup(jobType);
  console.log(`[worker] Initializing ${jobType} worker...`);

  // Set up Redis pub/sub for notifications
  const { statusChannel, streamName, groupName, consumerName } =
    getJobKeys(jobType);
  const subscriber = redis.duplicate();

  // Process any existing jobs in the stream first
  await processStreamMessages(
    jobType,
    streamName,
    groupName,
    consumerName,
    handler,
    options
  );

  // Subscribe to the status channel for notifications
  await subscriber.subscribe(statusChannel);
  console.log(
    `[worker] Subscribed to ${statusChannel} channel for notifications`
  );

  // Listen for notifications to process new jobs
  subscriber.on("message", async (channel, message) => {
    if (channel === statusChannel) {
      console.log(
        `[worker] Received notification on ${statusChannel}: ${message}`
      );
      await processStreamMessages(
        jobType,
        streamName,
        groupName,
        consumerName,
        handler,
        options
      );
    }
  });

  // Keep the main function running
  return new Promise(() => {});
}

// Process messages from a Redis stream (non-polling)
async function processStreamMessages(
  jobType: JobType,
  streamName: string,
  groupName: string,
  consumerName: string,
  handler: (job: any) => Promise<void>,
  options = { count: 10, blockMs: 5000 }
) {
  try {
    console.log(`[worker] Processing messages from ${streamName}`);

    // Read messages from the stream (one-time read, not continuous polling)
    const res = (await redis.xreadgroup(
      "GROUP",
      groupName,
      consumerName,
      "COUNT",
      options.count,
      "STREAMS",
      streamName,
      ">"
    )) as [string, [string, [string, string][]][]] | null;

    if (!res) {
      console.log(`[worker] No new messages in ${streamName}`);
      return;
    }

    console.log(
      `[worker] Processing ${res[0][1].length} messages from ${streamName}`
    );

    // Process each message
    for (const [, messages] of res) {
      for (const [id, entries] of messages) {
        try {
          // Find the job data
          let jobData: any = null;

          // Directly access the job data from entries
          if (entries && entries.length >= 2 && entries[0] === "job") {
            try {
              jobData = JSON.parse(entries[1]);
            } catch (parseErr) {
              console.error(`[worker] Error parsing job data:`, parseErr);
            }
          } else {
            // Try the old way as fallback
            for (const entry of entries) {
              if (Array.isArray(entry) && entry[0] === "job") {
                try {
                  jobData = JSON.parse(entry[1]);
                  break;
                } catch (parseErr) {
                  console.error(
                    `[worker] Error parsing job data (fallback):`,
                    parseErr
                  );
                }
              }
            }
          }

          if (!jobData) {
            console.error(`[worker] No job data found in message:`, entries);
            await redis.xack(streamName, groupName, id as string);
            continue;
          }

          const jobId = jobData.jobId ?? jobData.id;
          if (!jobId) {
            console.error(`[worker] No job ID found in job data:`, jobData);
            await redis.xack(streamName, groupName, id as string);
            continue;
          }

          console.log(`[worker] Processing job ${jobId} from ${streamName}`);

          // Update job status in database
          try {
            // First check if the record exists
            const existingRecord = await prisma.workerProcess.findUnique({
              where: { id: jobId },
            });

            if (existingRecord) {
              // Update existing record
              await prisma.workerProcess.update({
                where: { id: jobId },
                data: {
                  status: JobStatus.PROCESSING,
                  startedAt: new Date(),
                },
              });
              console.log(
                `[worker-debug] Updated existing WorkerProcess record for job ${jobId}`
              );
            } else {
              // Create a new record
              await prisma.workerProcess.create({
                data: {
                  id: jobId,
                  type: jobType,
                  status: JobStatus.PROCESSING,
                  data: jobData,
                  createdAt: new Date(),
                  updatedAt: new Date(),
                  startedAt: new Date(),
                },
              });
              console.log(
                `[worker-debug] Created new WorkerProcess record for job ${jobId}`
              );
            }
          } catch (dbErr) {
            console.error(
              `[worker] Failed to update/create WorkerProcess record:`,
              dbErr
            );
            // Continue even if database operation fails
          }

          // Process the job
          try {
            await handler(jobData);

            // Update job status in database
            try {
              // First check if the record exists
              const existingRecord = await prisma.workerProcess.findUnique({
                where: { id: jobId },
              });

              if (existingRecord) {
                // Update existing record
                await prisma.workerProcess.update({
                  where: { id: jobId },
                  data: {
                    status: JobStatus.COMPLETED,
                    completedAt: new Date(),
                  },
                });
                console.log(
                  `[worker-debug] Updated WorkerProcess record for completed job ${jobId}`
                );
              } else {
                // Create a new record for the completed job
                await prisma.workerProcess.create({
                  data: {
                    id: jobId,
                    type: jobType,
                    status: JobStatus.COMPLETED,
                    data: jobData,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    startedAt: new Date(),
                    completedAt: new Date(),
                  },
                });
                console.log(
                  `[worker-debug] Created new WorkerProcess record for completed job ${jobId}`
                );
              }
            } catch (dbErr) {
              console.error(
                `[worker] Failed to update/create WorkerProcess record for completed job:`,
                dbErr
              );
            }

            console.log(`[worker] Completed job ${jobId}`);
          } catch (handlerErr) {
            console.error(
              `[worker] Error processing job ${jobId}:`,
              handlerErr
            );

            // Update job status to failed
            try {
              // First check if the record exists
              const existingRecord = await prisma.workerProcess.findUnique({
                where: { id: jobId },
              });

              const errorMessage =
                handlerErr instanceof Error
                  ? handlerErr.message
                  : String(handlerErr);

              if (existingRecord) {
                // Update existing record
                await prisma.workerProcess.update({
                  where: { id: jobId },
                  data: {
                    status: JobStatus.FAILED,
                    error: errorMessage,
                  },
                });
                console.log(
                  `[worker-debug] Updated WorkerProcess record for failed job ${jobId}`
                );
              } else {
                // Create a new record for the failed job
                await prisma.workerProcess.create({
                  data: {
                    id: jobId,
                    type: jobType,
                    status: JobStatus.FAILED,
                    data: jobData,
                    error: errorMessage,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    startedAt: new Date(),
                  },
                });
                console.log(
                  `[worker-debug] Created new WorkerProcess record for failed job ${jobId}`
                );
              }
            } catch (updateErr) {
              console.error(
                `[worker] Failed to update/create WorkerProcess record for failed job:`,
                updateErr
              );
            }
          }

          // Acknowledge the message
          await redis.xack(streamName, groupName, id as string);
        } catch (err) {
          console.error(`[worker] Error processing message:`, err);
          // Still acknowledge the message to avoid reprocessing
          await redis.xack(streamName, groupName, id as string);
        }
      }
    }
  } catch (err) {
    console.error(
      `[worker] Error processing messages from ${streamName}:`,
      err
    );
  }
}

// Note: Database polling has been removed in favor of Redis streams only

// Publish job status update
export async function publishJobStatus(jobType: JobType, statusData: any) {
  const { statusChannel } = getJobKeys(jobType);

  try {
    await redis.publish(statusChannel, JSON.stringify(statusData));
    console.log(`[redis] Published status update for ${jobType} job.`);
  } catch (err) {
    console.error(
      `[redis] Failed to publish status update for ${jobType} job:`,
      err
    );
  }
}
