import {
  add_locations,
  check_target,
  each,
  element,
  hmr,
  index,
  legacy_api,
  prop,
  rest_props,
  set_attributes,
  snippet,
  validate_dynamic_element_tag
} from "./chunk-NDLSR7SS.js";
import {
  append,
  comment,
  ns_template
} from "./chunk-4ZPQDFFK.js";
import {
  FILENAME,
  HMR,
  child,
  first_child,
  get,
  noop,
  pop,
  push,
  reset,
  set,
  sibling,
  template_effect
} from "./chunk-QYXENPIM.js";

// node_modules/@lucide/svelte/dist/defaultAttributes.js
var defaultAttributes = {
  xmlns: "http://www.w3.org/2000/svg",
  width: 24,
  height: 24,
  viewBox: "0 0 24 24",
  fill: "none",
  stroke: "currentColor",
  "stroke-width": 2,
  "stroke-linecap": "round",
  "stroke-linejoin": "round"
};
var defaultAttributes_default = defaultAttributes;

// node_modules/@lucide/svelte/dist/Icon.svelte
Icon[FILENAME] = "node_modules/@lucide/svelte/dist/Icon.svelte";
var root = add_locations(ns_template(`<svg><!><!></svg>`), Icon[FILENAME], [[5, 0]]);
function Icon($$anchor, $$props) {
  check_target(new.target);
  push($$props, true, Icon);
  const color = prop($$props, "color", 3, "currentColor"), size = prop($$props, "size", 3, 24), strokeWidth = prop($$props, "strokeWidth", 3, 2), absoluteStrokeWidth = prop($$props, "absoluteStrokeWidth", 3, false), iconNode = prop($$props, "iconNode", 19, () => []), props = rest_props(
    $$props,
    [
      "$$slots",
      "$$events",
      "$$legacy",
      "name",
      "color",
      "size",
      "strokeWidth",
      "absoluteStrokeWidth",
      "iconNode",
      "children"
    ],
    "props"
  );
  var svg = root();
  let attributes;
  var node = child(svg);
  each(node, 17, iconNode, index, ($$anchor2, $$item) => {
    let tag = () => get($$item)[0];
    tag();
    let attrs = () => get($$item)[1];
    attrs();
    var fragment = comment();
    var node_1 = first_child(fragment);
    validate_dynamic_element_tag(tag);
    element(
      node_1,
      tag,
      true,
      ($$element, $$anchor3) => {
        let attributes_1;
        template_effect(() => attributes_1 = set_attributes($$element, attributes_1, { ...attrs() }));
      },
      void 0,
      [15, 4]
    );
    append($$anchor2, fragment);
  });
  var node_2 = sibling(node);
  snippet(node_2, () => $$props.children ?? noop);
  reset(svg);
  template_effect(
    ($0) => attributes = set_attributes(svg, attributes, {
      ...defaultAttributes_default,
      ...props,
      width: size(),
      height: size(),
      stroke: color(),
      "stroke-width": $0,
      class: [
        "lucide-icon lucide",
        $$props.name && `lucide-${$$props.name}`,
        $$props.class
      ]
    }),
    [
      () => absoluteStrokeWidth() ? Number(strokeWidth()) * 24 / Number(size()) : strokeWidth()
    ]
  );
  append($$anchor, svg);
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  Icon = hmr(Icon, () => Icon[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Icon[HMR].source;
    set(Icon[HMR].source, module.default[HMR].original);
  });
}
var Icon_default = Icon;

export {
  Icon_default
};
/*! Bundled license information:

@lucide/svelte/dist/defaultAttributes.js:
  (**
   * @license @lucide/svelte v0.482.0 - ISC
   *
   * ISC License
   * 
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   * 
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   * 
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   * 
   *)
*/
//# sourceMappingURL=chunk-KCINTJ2M.js.map
