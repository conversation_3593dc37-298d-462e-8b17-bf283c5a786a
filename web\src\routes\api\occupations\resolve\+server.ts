import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import type { RequestHandler } from './$types';

// Resolve occupation IDs to their titles
export const POST: RequestHandler = async ({ request }) => {
  try {
    const { ids } = await request.json();
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return json([]);
    }

    const occupations = await prisma.occupation.findMany({
      where: {
        id: {
          in: ids,
        },
      },
      select: {
        id: true,
        title: true,
        shortTitle: true,
      },
    });

    return json(occupations);
  } catch (error) {
    console.error('Error resolving occupation IDs:', error);
    return json({ error: 'Failed to resolve occupation IDs' }, { status: 500 });
  }
};
