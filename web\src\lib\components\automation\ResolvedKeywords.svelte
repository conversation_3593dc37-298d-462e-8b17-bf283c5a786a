<script lang="ts">
  interface Props {
    keywordIds: string;
    fallback?: string;
  }

  let { keywordIds, fallback = 'None specified' }: Props = $props();

  let resolvedKeywords = $state('');
  let isLoading = $state(true);

  // Cache for resolved keywords to avoid repeated API calls
  const keywordCache = new Map<string, string>();

  async function resolveKeywords() {
    if (!keywordIds) {
      resolvedKeywords = fallback;
      isLoading = false;
      return;
    }

    // Check cache first
    if (keywordCache.has(keywordIds)) {
      resolvedKeywords = keywordCache.get(keywordIds)!;
      isLoading = false;
      return;
    }

    try {
      const ids = keywordIds.split(', ').filter(Boolean);
      if (ids.length === 0) {
        resolvedKeywords = fallback;
        isLoading = false;
        return;
      }

      const response = await fetch(`/api/occupations/resolve`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ids })
      });

      if (response.ok) {
        const occupations = await response.json();
        const resolved = occupations.map((occ: any) => occ.title).join(', ');
        resolvedKeywords = resolved || fallback;
        
        // Cache the result
        keywordCache.set(keywordIds, resolvedKeywords);
      } else {
        resolvedKeywords = keywordIds; // Fallback to IDs
      }
    } catch (error) {
      console.error('Error resolving keywords:', error);
      resolvedKeywords = keywordIds; // Fallback to IDs
    } finally {
      isLoading = false;
    }
  }

  // Resolve keywords when component mounts or keywordIds change
  $effect(() => {
    isLoading = true;
    resolveKeywords();
  });
</script>

{#if isLoading}
  <span class="text-gray-400">Loading...</span>
{:else}
  <span>{resolvedKeywords}</span>
{/if}
