import { PrismaClient } from "@prisma/client";
import {
  <PERSON>Type,
  JobStatus,
  processJobs,
  publishJob<PERSON>tatus,
  addJob,
} from "../utils/redis-jobs.js";
import { createNotification } from "../utils/notification-service.js";
import { redis } from "../redis.js";

// Define a new job type for automation
const AUTOMATION_JOB_TYPE = "automation-job-search";

// Queue management constants
const MAX_CONCURRENT_WORKERS = 3; // Limit concurrent automation runs
const QUEUE_NAME = "automation:job-queue";
const PROCESSING_SET = "automation:processing";

// Create a Prisma client for database operations
const prisma = new PrismaClient();

// Create a separate Prisma client for cron schema (job listings)
const cronPrisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
});

// Define a type for the extended PrismaClient with optional models
type ExtendedPrismaClient = PrismaClient & {
  automationRun?: any;
  profile?: any;
  jobListing?: any;
  resume?: any;
};

// Cast the prisma client to the extended type to avoid TypeScript errors
const extendedPrisma = prisma as ExtendedPrismaClient;

// Track if we've already subscribed to Redis channels
let redisSubscribed = false;

// Helper function to send notification using the same pattern as resume parsing
async function sendAutomationNotification(notificationData: {
  userId: string;
  title: string;
  message: string;
  url?: string;
  type: string;
  priority: string;
  metadata?: any;
}) {
  try {
    // Use the same notification service as resume parsing
    await createNotification({
      userId: notificationData.userId,
      title: notificationData.title,
      message: notificationData.message,
      url: notificationData.url,
      type: notificationData.type as any,
      priority: notificationData.priority as any,
      data: notificationData.metadata,
    });

    console.log(
      `[automation] Sent ${notificationData.type} notification to user ${notificationData.userId}`
    );
  } catch (error) {
    console.error("[automation] Error sending notification:", error);
  }
}

// Queue management functions
async function addToQueue(automationData: any): Promise<void> {
  const queueItem = {
    ...automationData,
    queuedAt: new Date().toISOString(),
    priority: 1, // Default priority
  };

  // Add to queue (FIFO)
  await redis.lpush(QUEUE_NAME, JSON.stringify(queueItem));
  console.log(`[queue] Added automation run ${automationData.runId} to queue`);
}

async function getQueueLength(): Promise<number> {
  return await redis.llen(QUEUE_NAME);
}

async function getCurrentlyProcessing(): Promise<number> {
  return await redis.scard(PROCESSING_SET);
}

async function canProcessNewJob(): Promise<boolean> {
  const currentlyProcessing = await getCurrentlyProcessing();
  return currentlyProcessing < MAX_CONCURRENT_WORKERS;
}

async function markAsProcessing(runId: string): Promise<void> {
  await redis.sadd(PROCESSING_SET, runId);
  console.log(`[queue] Marked run ${runId} as processing`);
}

async function markAsCompleted(runId: string): Promise<void> {
  await redis.srem(PROCESSING_SET, runId);
  console.log(`[queue] Marked run ${runId} as completed`);
}

async function processQueue(): Promise<void> {
  while (await canProcessNewJob()) {
    // Get next job from queue
    const queueItem = await redis.rpop(QUEUE_NAME);
    if (!queueItem) {
      break; // No more jobs in queue
    }

    try {
      const automationData = JSON.parse(queueItem);
      console.log(
        `[queue] Processing automation run ${automationData.runId} from queue`
      );

      // Mark as processing
      await markAsProcessing(automationData.runId);

      // Create worker job
      const jobId = await addJob(JobType.SEARCH, {
        ...automationData,
        type: AUTOMATION_JOB_TYPE,
      });

      console.log(
        `[queue] Created job ${jobId} for automation run ${automationData.runId}`
      );
    } catch (error) {
      console.error("[queue] Error processing queue item:", error);
    }
  }
}

// Subscribe to automation job search channel
async function subscribeToAutomationChannel() {
  // Skip if already subscribed
  if (redisSubscribed) {
    console.log("[automation] Already subscribed to Redis channels, skipping");
    return;
  }

  try {
    // Create a duplicate Redis client for pub/sub without connecting it
    // This is necessary because a Redis client in subscriber mode cannot be used for other commands
    const subscriber = redis.duplicate();

    // Log the subscriber status
    console.log(
      `[automation] Subscriber Redis client status: ${subscriber.status}`
    );

    // For ioredis, we don't need to explicitly connect a duplicated client
    // It inherits the connection from the parent client
    // Just proceed with subscription without calling connect()

    // Subscribe to the automation job search channel
    console.log("[automation] Subscribing to job-search channel...");
    await subscriber.subscribe("automation:job-search");

    subscriber.on("message", async (channel: string, message: string) => {
      if (channel !== "automation:job-search") return;
      try {
        // Check if message is valid
        if (!message) {
          console.log(
            "[automation] Received empty job search message, ignoring"
          );
          return;
        }

        // Parse the message data
        const data = JSON.parse(message);
        console.log("[automation] Received job search request:", data);

        // Validate required fields
        if (!data ?? !data.runId) {
          console.log("[automation] Invalid job search data, missing runId");
          return;
        }

        // Check if we can process immediately or need to queue
        if (await canProcessNewJob()) {
          // Mark as processing immediately
          await markAsProcessing(data.runId);

          // Create a worker process record
          const jobId = await addJob(JobType.SEARCH, {
            ...data,
            type: AUTOMATION_JOB_TYPE,
          });

          console.log(
            `[automation] Created job ${jobId} for automation run ${data.runId} (immediate processing)`
          );
        } else {
          // Add to queue
          await addToQueue(data);

          // Update status to queued
          try {
            if (extendedPrisma.automationRun) {
              await extendedPrisma.automationRun.update({
                where: { id: data.runId },
                data: {
                  status: "queued",
                  progress: 0,
                },
              });
            }
          } catch (dbError) {
            console.error(
              "[automation] Error updating automation run status:",
              dbError
            );
          }

          const queueLength = await getQueueLength();
          console.log(
            `[automation] Added automation run ${data.runId} to queue (position: ${queueLength})`
          );
        }

        // Try to update the automation run status if processing immediately
        if ((await getCurrentlyProcessing()) > 0) {
          try {
            if (extendedPrisma.automationRun) {
              await extendedPrisma.automationRun.update({
                where: { id: data.runId },
                data: {
                  status: "running",
                  startedAt: new Date(),
                },
              });
            }
          } catch (dbError) {
            console.error(
              "[automation] Error updating automation run status:",
              dbError
            );
          }
        }
      } catch (error) {
        console.error("[automation] Error processing message:", error);
      }
    });

    // Also subscribe to stop channel
    try {
      console.log("[automation] Subscribing to stop channel...");
      await subscriber.subscribe("automation:stop");

      subscriber.on("message", async (channel: string, message: string) => {
        if (channel !== "automation:stop") return;
        try {
          // Check if message is valid
          if (!message) {
            console.log("[automation] Received empty stop message, ignoring");
            return;
          }

          // Parse the message data
          const data = JSON.parse(message);
          console.log("[automation] Received stop request:", data);

          // Validate required fields
          if (!data ?? !data.runId) {
            console.log("[automation] Invalid stop data, missing runId");
            return;
          }

          // Find any running jobs for this automation run
          try {
            const runningJobs = await extendedPrisma.workerProcess.findMany({
              where: {
                data: {
                  path: ["runId"],
                  equals: data.runId,
                },
                status: JobStatus.PROCESSING,
              },
            });

            // Mark jobs as failed
            for (const job of runningJobs) {
              await extendedPrisma.workerProcess.update({
                where: { id: job.id },
                data: {
                  status: JobStatus.FAILED,
                  error: "Stopped by user",
                },
              });

              // Publish status update
              await publishJobStatus(JobType.SEARCH, {
                jobId: job.id,
                status: JobStatus.FAILED,
                message: "Job stopped by user",
              });
            }

            console.log(
              `[automation] Marked ${runningJobs.length} jobs as stopped`
            );
          } catch (dbError) {
            console.error(
              "[automation] Error updating worker processes:",
              dbError
            );
            // Continue processing even if database update fails
          }

          // Try to update the automation run status if the model exists
          try {
            if (extendedPrisma.automationRun) {
              await extendedPrisma.automationRun.update({
                where: { id: data.runId },
                data: {
                  status: "stopped",
                  stoppedAt: new Date(),
                },
              });
              console.log(`[automation] Stopped automation run ${data.runId}`);

              // Send stop notification using direct approach
              try {
                await sendAutomationNotification({
                  userId: data.userId,
                  title: "Automation Run Stopped",
                  message: `Your automation run was stopped manually.`,
                  type: "warning",
                  url: `/dashboard/automation/${data.runId}`,
                  priority: "medium",
                  metadata: {
                    automationRunId: data.runId,
                    status: "stopped",
                  },
                });

                // Publish status update to Redis for WebSocket
                try {
                  await redis.publish(
                    "automation:status",
                    JSON.stringify({
                      runId: data.runId,
                      userId: data.userId,
                      status: "stopped",
                    })
                  );
                } catch (redisError) {
                  console.error(
                    "[automation] Error publishing status to Redis:",
                    redisError
                  );
                }
              } catch (notificationError) {
                console.error(
                  "[automation] Error sending stop notification:",
                  notificationError
                );
                // Don't fail the operation if notification fails
              }
            } else {
              console.log(
                "[automation] automationRun model not available, skipping status update"
              );
            }
          } catch (dbError) {
            console.error(
              "[automation] Error updating automation run status:",
              dbError
            );
            // Continue processing even if database update fails
          }
        } catch (error) {
          console.error("[automation] Error processing stop message:", error);
        }
      });

      console.log("[automation] Subscribed to automation channels");

      // Mark as subscribed so we don't try again
      redisSubscribed = true;
    } catch (error) {
      console.error("[automation] Error subscribing to stop channel:", error);
      throw error; // Re-throw to be caught by the handleAutomationJob function
    }
  } catch (error) {
    console.error("[automation] Error setting up Redis subscriptions:", error);
    throw error; // Re-throw to be caught by the handleAutomationJob function
  }
}

// Handle automation search jobs with enhanced matching
async function handleAutomationJob(job: any) {
  const {
    runId,
    profileId,
    keywords,
    location,
    resumeId,
    jobId,
    maxJobsToApply = 10,
    minMatchScore = 70,
    autoApplyEnabled = false,
    salaryMin,
    salaryMax,
    experienceLevelMin,
    experienceLevelMax,
    jobTypes = [],
    remotePreference = "any",
    companySizePreference = [],
    excludeCompanies = [],
    preferredCompanies = [],
    specifications = {},
  } = job;
  console.log(`[worker] Processing enhanced automation job for run: ${runId}`);

  // Subscribe to Redis channels if needed for this job
  try {
    console.log(
      "[automation] Subscribing to Redis channels for job processing..."
    );
    await subscribeToAutomationChannel();
    console.log("[automation] Successfully subscribed to Redis channels");
  } catch (subscribeError) {
    console.error(
      "[automation] Failed to subscribe to Redis channels:",
      subscribeError
    );
    console.log(
      "[automation] Continuing with job processing despite subscription failure"
    );
  }

  try {
    // Try to update the automation run status if the model exists
    try {
      if (extendedPrisma.automationRun) {
        await extendedPrisma.automationRun.update({
          where: { id: runId },
          data: {
            status: "running",
            progress: 10,
          },
        });
      } else {
        console.log(
          "[automation] automationRun model not available, skipping status update"
        );
      }
    } catch (dbError) {
      console.error(
        "[automation] Error updating automation run status:",
        dbError
      );
      // Continue processing even if database update fails
    }

    // Mock profile data if we can't access the database model
    let profile = null;
    try {
      if (extendedPrisma.profile) {
        profile = await extendedPrisma.profile.findUnique({
          where: { id: profileId },
          include: {
            data: true,
            resumes: {
              include: {
                document: true,
              },
            },
          },
        });
      } else {
        console.log(
          "[automation] profile model not available, using mock data"
        );
        // Create mock profile data
        profile = {
          id: profileId,
          data: {
            title: "Software Engineer",
            skills: ["JavaScript", "TypeScript", "React", "Node.js"],
          },
        };
      }
    } catch (dbError) {
      console.error("[automation] Error fetching profile data:", dbError);
      // Create mock profile data as fallback
      profile = {
        id: profileId,
        data: {
          title: "Software Engineer",
          skills: ["JavaScript", "TypeScript", "React", "Node.js"],
        },
      };
    }

    // Get resume data if available (or use mock data)
    let resume = null;
    if (resumeId) {
      try {
        if (extendedPrisma.resume) {
          resume = await extendedPrisma.resume.findUnique({
            where: { id: resumeId },
            // Skip the document include to avoid errors
          });
        }
      } catch (dbError) {
        console.error("[automation] Error fetching resume data:", dbError);
      }
    }

    // Try to update progress
    try {
      if (extendedPrisma.automationRun) {
        await extendedPrisma.automationRun.update({
          where: { id: runId },
          data: {
            progress: 20,
          },
        });
      }
    } catch (dbError) {
      console.error("[automation] Error updating progress:", dbError);
    }

    // Search real job listings from database
    const matchedJobListings = await searchJobListingsFromDatabase(profile, {
      keywords: keywords ?? "",
      location: location ?? "",
      maxJobsToApply,
      minMatchScore,
      salaryMin,
      salaryMax,
      experienceLevelMin,
      experienceLevelMax,
      jobTypes,
      remotePreference,
      companySizePreference,
      excludeCompanies,
      preferredCompanies,
    });

    // Try to update progress
    try {
      if (extendedPrisma.automationRun) {
        await extendedPrisma.automationRun.update({
          where: { id: runId },
          data: {
            progress: 80,
          },
        });
      }
    } catch (dbError) {
      console.error("[automation] Error updating progress:", dbError);
    }

    // Try to update the automation run status
    try {
      if (extendedPrisma.automationRun) {
        await extendedPrisma.automationRun.update({
          where: { id: runId },
          data: {
            status: "completed",
            progress: 100,
            completedAt: new Date(),
          },
        });
      }
    } catch (dbError) {
      console.error(
        "[automation] Error updating automation run status:",
        dbError
      );
    }

    // Store results in AutomationRun - just the job IDs and match data
    const jobIds = matchedJobListings.map((job: any) => job.id);
    const matchData = matchedJobListings.reduce((acc: any, job: any) => {
      acc[job.id] = {
        matchScore: job.matchScore,
        matchReasons: job.matchReasons,
        skillsMatch: job.skillsMatch,
        experienceMatch: job.experienceMatch,
      };
      return acc;
    }, {});

    try {
      if (extendedPrisma.automationRun) {
        await extendedPrisma.automationRun.update({
          where: { id: runId },
          data: {
            status: "completed",
            progress: 100,
            completedAt: new Date(),
            jobsFound: matchedJobListings.length,
            avgMatchScore:
              matchedJobListings.reduce(
                (sum: number, job: any) => sum + job.matchScore,
                0
              ) / matchedJobListings.length || 0,
            matchedJobIds: jobIds,
            jobMatchData: matchData,
          },
        });
      }
    } catch (dbError) {
      console.error("[automation] Error updating automation run:", dbError);
    }

    // Store results in the WorkerProcess record
    try {
      await extendedPrisma.workerProcess.update({
        where: { id: jobId },
        data: {
          data: {
            ...job,
            results: jobIds,
          },
          status: JobStatus.COMPLETED,
          completedAt: new Date(),
        },
      });
    } catch (dbError) {
      console.error("[automation] Error updating worker process:", dbError);
    }

    // Publish status update
    await publishJobStatus(JobType.SEARCH, {
      jobId,
      status: JobStatus.COMPLETED,
      message: `Job search for automation run ${runId} completed.`,
      resultCount: matchedJobListings.length,
    });

    console.log(`[worker] Completed automation job for run: ${runId}`);

    // Send completion notification via Redis stream
    try {
      // Get the automation run to find the user ID
      const automationRun = await extendedPrisma.automationRun.findUnique({
        where: { id: runId },
        select: { userId: true },
      });

      if (automationRun?.userId) {
        await sendAutomationNotification({
          userId: automationRun.userId,
          title: "Automation Run Completed",
          message: `Your automation run has completed successfully. ${matchedJobListings.length} jobs were processed.`,
          type: "success",
          url: `/dashboard/automation/${runId}`,
          priority: "medium",
          metadata: {
            automationRunId: runId,
            status: "completed",
            jobsFound: matchedJobListings.length,
          },
        });

        // Publish status update to Redis for WebSocket
        try {
          await redis.publish(
            "automation:status",
            JSON.stringify({
              runId,
              userId: automationRun.userId,
              status: "completed",
              progress: 100,
              jobsFound: matchedJobListings.length,
            })
          );
        } catch (redisError) {
          console.error(
            "[automation] Error publishing status to Redis:",
            redisError
          );
        }
      }
    } catch (notificationError) {
      console.error(
        "[automation] Error sending completion notification:",
        notificationError
      );
      // Don't fail the job if notification fails
    }

    // Mark as completed and process queue
    await markAsCompleted(runId);
    await processQueue();
  } catch (err) {
    console.error(`[worker] Failed to process automation job:`, err);

    // Mark as completed even on failure and process queue
    await markAsCompleted(runId);
    await processQueue();

    // Try to update the automation run status
    try {
      if (extendedPrisma.automationRun) {
        await extendedPrisma.automationRun.update({
          where: { id: runId },
          data: {
            status: "failed",
            error: err instanceof Error ? err.message : String(err),
            failedAt: new Date(),
          },
        });
      }
    } catch (dbError) {
      console.error(
        "[automation] Error updating automation run status:",
        dbError
      );
    }

    // Publish failure status
    await publishJobStatus(JobType.SEARCH, {
      jobId,
      status: JobStatus.FAILED,
      message: `Job search for automation run ${runId} failed.`,
      error: err instanceof Error ? err.message : String(err),
    });

    // Send failure notification
    try {
      // Get the automation run to find the user ID
      const automationRun = await extendedPrisma.automationRun.findUnique({
        where: { id: runId },
        select: { userId: true },
      });

      if (automationRun?.userId) {
        await sendAutomationNotification({
          userId: automationRun.userId,
          title: "Automation Run Failed",
          message: `Your automation run encountered an error and could not complete. ${err instanceof Error ? err.message : "Please check the details and try again."}`,
          type: "error",
          url: `/dashboard/automation/${runId}`,
          priority: "medium",
          metadata: {
            automationRunId: runId,
            status: "failed",
            error: err instanceof Error ? err.message : String(err),
          },
        });

        // Publish status update to Redis for WebSocket
        try {
          await redis.publish(
            "automation:status",
            JSON.stringify({
              runId,
              userId: automationRun.userId,
              status: "failed",
              error: err instanceof Error ? err.message : String(err),
            })
          );
        } catch (redisError) {
          console.error(
            "[automation] Error publishing status to Redis:",
            redisError
          );
        }
      }
    } catch (notificationError) {
      console.error(
        "[automation] Error sending failure notification:",
        notificationError
      );
      // Don't fail the job if notification fails
    }

    // Re-throw to let the processJobs handler update the job status
    throw err;
  }
}

// Search real job listings from database with intelligent matching

// Search real job listings from database with intelligent matching
async function searchJobListingsFromDatabase(profile: any, config: any) {
  console.log(`[db-search] Searching job listings for profile: ${profile.id}`);
  console.log(`[db-search] Search config:`, config);

  // Extract profile data for matching
  const profileData = profile.data?.data ?? profile.data ?? {};
  const skills = extractSkills(profileData);
  const experienceYears = calculateExperienceYears(profileData);
  const jobTitles = extractJobTitles(profileData);

  console.log(`[db-search] Profile skills: ${skills.join(", ")}`);
  console.log(`[db-search] Experience years: ${experienceYears}`);
  console.log(`[db-search] Job titles: ${jobTitles.join(", ")}`);

  try {
    // Build database filter using Prisma
    const whereClause: any = {
      isActive: true,
    };

    // Keyword search in title and description
    if (config.keywords) {
      whereClause.OR = [
        {
          title: {
            contains: config.keywords,
            mode: "insensitive",
          },
        },
        {
          description: {
            contains: config.keywords,
            mode: "insensitive",
          },
        },
      ];
    }

    // Location filter
    if (config.location && config.location !== "") {
      whereClause.location = {
        contains: config.location,
        mode: "insensitive",
      };
    }

    // Salary filters
    if (config.salaryMin || config.salaryMax) {
      whereClause.AND = whereClause.AND || [];

      if (config.salaryMin) {
        whereClause.AND.push({
          OR: [
            { salaryMax: { gte: config.salaryMin } },
            { salaryMin: { gte: config.salaryMin } },
          ],
        });
      }

      if (config.salaryMax) {
        whereClause.AND.push({
          OR: [
            { salaryMin: { lte: config.salaryMax } },
            { salaryMax: { lte: config.salaryMax } },
          ],
        });
      }
    }

    // Remote preference
    if (config.remotePreference && config.remotePreference !== "any") {
      if (config.remotePreference === "remote") {
        whereClause.isRemoteFriendly = true;
      } else if (config.remotePreference === "onsite") {
        whereClause.isRemoteFriendly = false;
      }
    }

    // Exclude companies
    if (config.excludeCompanies && config.excludeCompanies.length > 0) {
      whereClause.company = {
        notIn: config.excludeCompanies,
      };
    }

    // Query job listings from cron schema using raw SQL
    let sqlQuery = `
      SELECT id, title, company, location, description, salary,
             "salaryMin", "salaryMax", "jobType", "experienceYears",
             "requiredSkills", "isRemote", "remoteType", "companySize",
             "postedAt", "isActive"
      FROM cron.job_listing
      WHERE "isActive" = true
    `;

    const queryParams: any[] = [];
    let paramIndex = 1;

    // Add keyword search
    if (config.keywords) {
      sqlQuery += ` AND (title ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`;
      queryParams.push(`%${config.keywords}%`);
      paramIndex++;
    }

    // Add location filter
    if (config.location && config.location !== "") {
      sqlQuery += ` AND location ILIKE $${paramIndex}`;
      queryParams.push(`%${config.location}%`);
      paramIndex++;
    }

    // Add salary filters
    if (config.salaryMin) {
      sqlQuery += ` AND ("salaryMax" >= $${paramIndex} OR "salaryMin" >= $${paramIndex})`;
      queryParams.push(config.salaryMin);
      paramIndex++;
    }

    if (config.salaryMax) {
      sqlQuery += ` AND ("salaryMin" <= $${paramIndex} OR "salaryMax" <= $${paramIndex})`;
      queryParams.push(config.salaryMax);
      paramIndex++;
    }

    // Add remote preference
    if (config.remotePreference && config.remotePreference !== "any") {
      if (config.remotePreference === "remote") {
        sqlQuery += ` AND "isRemote" = true`;
      } else if (config.remotePreference === "onsite") {
        sqlQuery += ` AND "isRemote" = false`;
      }
    }

    // Add company exclusions
    if (config.excludeCompanies && config.excludeCompanies.length > 0) {
      const placeholders = config.excludeCompanies
        .map(() => `$${paramIndex++}`)
        .join(", ");
      sqlQuery += ` AND company NOT IN (${placeholders})`;
      queryParams.push(...config.excludeCompanies);
    }

    // Add ordering and limit
    sqlQuery += ` ORDER BY "postedAt" DESC LIMIT $${paramIndex}`;
    queryParams.push(config.maxJobsToApply * 3); // Get more than needed for scoring

    console.log(`[db-search] Executing SQL query:`, sqlQuery);
    console.log(`[db-search] Query parameters:`, queryParams);

    const jobListings = await prisma.$queryRawUnsafe(sqlQuery, ...queryParams);

    console.log(
      `[db-search] Found ${(jobListings as any[]).length} potential job listings`
    );

    // Score and filter jobs using the matching algorithm
    const scoredJobs = (jobListings as any[])
      .map((job: any) => {
        const matchResult = calculateJobMatchScore(job, {
          profileId: profile.id,
          skills,
          experienceYears,
          jobTitles,
          salaryMin: config.salaryMin,
          salaryMax: config.salaryMax,
          remotePreference: config.remotePreference,
          companySizePreference: config.companySizePreference,
          excludeCompanies: config.excludeCompanies,
          preferredCompanies: config.preferredCompanies,
        });

        return {
          ...job,
          matchScore: matchResult.overallScore,
          matchReasons: matchResult.reasons,
          skillsMatch: matchResult.skillsMatch,
          experienceMatch: matchResult.experienceMatch,
        };
      })
      .filter((job: any) => job.matchScore >= config.minMatchScore)
      .sort((a: any, b: any) => b.matchScore - a.matchScore)
      .slice(0, config.maxJobsToApply);

    console.log(`[db-search] Filtered to ${scoredJobs.length} matching jobs`);
    console.log(
      `[db-search] Average match score: ${scoredJobs.reduce((sum: number, job: any) => sum + job.matchScore, 0) / scoredJobs.length || 0}%`
    );

    return scoredJobs;
  } catch (error) {
    console.error("[db-search] Error searching job listings:", error);
    return [];
  }
}

// Extract skills from profile data
function extractSkills(profileData: any): string[] {
  const skills = [];

  // Try different skill data structures
  if (profileData.skillsData?.list) {
    skills.push(...profileData.skillsData.list);
  } else if (Array.isArray(profileData.skills)) {
    skills.push(...profileData.skills);
  } else if (profileData.skills) {
    skills.push(profileData.skills);
  }

  // Extract skills from work experience
  if (profileData.workExperience) {
    profileData.workExperience.forEach((exp: any) => {
      if (exp.skills) {
        skills.push(...exp.skills);
      }
    });
  }

  return [...new Set(skills)].filter(Boolean); // Remove duplicates and empty values
}

// Calculate total years of experience
function calculateExperienceYears(profileData: any): number {
  if (!profileData.workExperience) return 0;

  let totalYears = 0;
  profileData.workExperience.forEach((exp: any) => {
    if (exp.startDate) {
      const start = new Date(exp.startDate);
      const end = exp.current
        ? new Date()
        : new Date(exp.endDate || new Date());
      const years =
        (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24 * 365);
      totalYears += Math.max(0, years);
    }
  });

  return Math.floor(totalYears);
}

// Extract job titles from work experience
function extractJobTitles(profileData: any): string[] {
  const titles = [];

  if (profileData.personalInfo?.jobTitle) {
    titles.push(profileData.personalInfo.jobTitle);
  }

  if (profileData.workExperience) {
    profileData.workExperience.forEach((exp: any) => {
      if (exp.title) {
        titles.push(exp.title);
      }
    });
  }

  return [...new Set(titles)].filter(Boolean);
}

// Calculate job match score (simplified version of the web utility)
function calculateJobMatchScore(job: any, criteria: any) {
  let overallScore = 0;
  const reasons: string[] = [];

  // Skills matching (40% weight)
  const jobSkills = job.requiredSkills || [];
  const matchedSkills = criteria.skills.filter((skill: string) =>
    jobSkills.some(
      (jobSkill: string) =>
        jobSkill.toLowerCase().includes(skill.toLowerCase()) ||
        skill.toLowerCase().includes(jobSkill.toLowerCase())
    )
  );
  const skillsScore =
    jobSkills.length > 0 ? (matchedSkills.length / jobSkills.length) * 100 : 50;
  overallScore += skillsScore * 0.4;
  if (matchedSkills.length > 0) {
    reasons.push(`Matched ${matchedSkills.length} skills`);
  }

  // Experience matching (25% weight)
  const requiredYears = job.experienceYears || 0;
  const experienceMatch = criteria.experienceYears >= requiredYears - 1;
  const experienceScore = experienceMatch
    ? 100
    : Math.max(0, 100 - (requiredYears - criteria.experienceYears) * 20);
  overallScore += experienceScore * 0.25;
  if (experienceMatch) {
    reasons.push("Experience level matches");
  }

  // Title matching (20% weight)
  const titleMatch = criteria.jobTitles.some(
    (title: string) =>
      job.title.toLowerCase().includes(title.toLowerCase()) ||
      title.toLowerCase().includes(job.title.toLowerCase())
  );
  const titleScore = titleMatch ? 100 : 30;
  overallScore += titleScore * 0.2;
  if (titleMatch) {
    reasons.push("Job title matches profile");
  }

  // Salary matching (10% weight)
  let salaryScore = 100;
  if (
    criteria.salaryMin &&
    job.salaryMax &&
    job.salaryMax < criteria.salaryMin
  ) {
    salaryScore = 0;
  }
  if (
    criteria.salaryMax &&
    job.salaryMin &&
    job.salaryMin > criteria.salaryMax
  ) {
    salaryScore = 0;
  }
  overallScore += salaryScore * 0.1;
  if (salaryScore > 0) {
    reasons.push("Salary meets requirements");
  }

  // Location/Remote matching (5% weight)
  let locationScore = 100;
  if (criteria.remotePreference === "remote" && !job.isRemote) {
    locationScore = 20;
  }
  overallScore += locationScore * 0.05;

  // Company preference bonus
  if (criteria.preferredCompanies.includes(job.company)) {
    overallScore += 10;
    reasons.push("Preferred company");
  }

  return {
    overallScore: Math.min(100, Math.max(0, overallScore)),
    reasons,
    skillsMatch: { matchedSkills, skillsScore },
    experienceMatch: { isMatch: experienceMatch, experienceScore },
  };
}

// Initialize immediately like other workers
console.log("[automation] Initializing automation worker...");

// Process jobs from the automation queue
processJobs(JobType.AUTOMATION, handleAutomationJob).catch((err) => {
  console.error("[automation] Error processing automation jobs:", err);
});

console.log("[automation] Worker started successfully");

// Handle process exit signals
process.on("SIGINT", async () => {
  console.log("Received SIGINT signal, shutting down automation worker...");
  await cleanup();
  process.exit(0);
});

process.on("SIGTERM", async () => {
  console.log("Received SIGTERM signal, shutting down automation worker...");
  await cleanup();
  console.log(
    "Cleanup completed, but keeping service alive to allow workers to finish."
  );
  // Removed process.exit(0) to keep the process alive
});

// Cleanup function to close connections
async function cleanup() {
  console.log("Cleaning up resources...");
  try {
    // Close Redis connection
    if (redis && redis.disconnect) {
      await redis.disconnect();
    }

    // Close Prisma connection
    if (extendedPrisma && extendedPrisma.$disconnect) {
      await extendedPrisma.$disconnect();
    }

    console.log("Cleanup completed successfully");
  } catch (error) {
    console.error("Error during cleanup:", error);
  }
}
