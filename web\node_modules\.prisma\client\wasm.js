
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.8.1
 * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e
 */
Prisma.prismaVersion = {
  client: "6.8.1",
  engine: "2060c79ba17c6bb9f5823312b6f6b7f4a845738e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.TeamScalarFieldEnum = {
  id: 'id',
  name: 'name',
  ownerId: 'ownerId',
  createdAt: 'createdAt'
};

exports.Prisma.TeamMemberScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  teamId: 'teamId',
  role: 'role',
  joinedAt: 'joinedAt'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  name: 'name',
  image: 'image',
  passwordHash: 'passwordHash',
  role: 'role',
  seats: 'seats',
  stripeCustomerId: 'stripeCustomerId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  preferences: 'preferences',
  isAdmin: 'isAdmin',
  emailVerified: 'emailVerified',
  verificationExpires: 'verificationExpires',
  verificationToken: 'verificationToken',
  provider: 'provider',
  providerId: 'providerId',
  referralCode: 'referralCode',
  referredById: 'referredById',
  referralCount: 'referralCount',
  referralRewards: 'referralRewards'
};

exports.Prisma.AccountScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  provider: 'provider',
  providerAccountId: 'providerAccountId',
  refresh_token: 'refresh_token',
  access_token: 'access_token',
  expires_at: 'expires_at',
  token_type: 'token_type',
  scope: 'scope',
  id_token: 'id_token',
  session_state: 'session_state'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  sessionToken: 'sessionToken',
  userId: 'userId',
  expires: 'expires',
  browser: 'browser',
  device: 'device',
  ip: 'ip',
  isRevoked: 'isRevoked',
  lastActive: 'lastActive',
  location: 'location',
  os: 'os',
  userAgent: 'userAgent'
};

exports.Prisma.VerificationTokenScalarFieldEnum = {
  identifier: 'identifier',
  token: 'token',
  expires: 'expires'
};

exports.Prisma.ApplicationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  company: 'company',
  position: 'position',
  location: 'location',
  appliedDate: 'appliedDate',
  status: 'status',
  nextAction: 'nextAction',
  notes: 'notes',
  url: 'url',
  jobType: 'jobType',
  resumeUploaded: 'resumeUploaded',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InterviewStageScalarFieldEnum = {
  id: 'id',
  applicationId: 'applicationId',
  stageName: 'stageName',
  stageDate: 'stageDate',
  outcome: 'outcome',
  feedback: 'feedback',
  interviewers: 'interviewers',
  duration: 'duration',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  nextAction: 'nextAction'
};

exports.Prisma.InterviewQuestionScalarFieldEnum = {
  id: 'id',
  interviewStageId: 'interviewStageId',
  question: 'question',
  category: 'category',
  difficulty: 'difficulty',
  userResponse: 'userResponse',
  userConfidence: 'userConfidence',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProfileScalarFieldEnum = {
  id: 'id',
  name: 'name',
  userId: 'userId',
  teamId: 'teamId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  defaultDocumentId: 'defaultDocumentId'
};

exports.Prisma.ProfileDataScalarFieldEnum = {
  id: 'id',
  profileId: 'profileId',
  data: 'data',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DocumentScalarFieldEnum = {
  id: 'id',
  label: 'label',
  fileUrl: 'fileUrl',
  type: 'type',
  contentType: 'contentType',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  isDefault: 'isDefault',
  profileId: 'profileId',
  userId: 'userId',
  teamId: 'teamId',
  fileSize: 'fileSize',
  pageCount: 'pageCount',
  fileName: 'fileName',
  filePath: 'filePath',
  storageLocation: 'storageLocation',
  storageType: 'storageType'
};

exports.Prisma.ResumeScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  documentId: 'documentId',
  score: 'score',
  updatedAt: 'updatedAt',
  isParsed: 'isParsed',
  parsedAt: 'parsedAt',
  rawText: 'rawText',
  parsedData: 'parsedData'
};

exports.Prisma.ResumeOptimizationScalarFieldEnum = {
  id: 'id',
  resumeId: 'resumeId',
  score: 'score',
  summary: 'summary',
  suggestions: 'suggestions',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SubscriptionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  stripeSessionId: 'stripeSessionId',
  stripePriceId: 'stripePriceId',
  planId: 'planId',
  quantity: 'quantity',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  cancelAtPeriodEnd: 'cancelAtPeriodEnd',
  canceledAt: 'canceledAt',
  currentPeriodEnd: 'currentPeriodEnd',
  currentPeriodStart: 'currentPeriodStart',
  pausedAt: 'pausedAt',
  resumeAt: 'resumeAt',
  status: 'status',
  stripeSubscriptionId: 'stripeSubscriptionId'
};

exports.Prisma.PlanScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  section: 'section',
  monthlyPrice: 'monthlyPrice',
  annualPrice: 'annualPrice',
  stripePriceMonthlyId: 'stripePriceMonthlyId',
  stripePriceYearlyId: 'stripePriceYearlyId',
  popular: 'popular',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PlanFeatureScalarFieldEnum = {
  id: 'id',
  planId: 'planId',
  featureId: 'featureId',
  accessLevel: 'accessLevel',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PlanFeatureLimitScalarFieldEnum = {
  id: 'id',
  planFeatureId: 'planFeatureId',
  limitId: 'limitId',
  value: 'value',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FeatureScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  category: 'category',
  icon: 'icon',
  beta: 'beta',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FeatureLimitScalarFieldEnum = {
  id: 'id',
  featureId: 'featureId',
  name: 'name',
  description: 'description',
  defaultValue: 'defaultValue',
  type: 'type',
  unit: 'unit',
  resetDay: 'resetDay',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FeatureRequirementScalarFieldEnum = {
  id: 'id',
  featureId: 'featureId',
  requiredFeatureId: 'requiredFeatureId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FeatureUsageScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  featureId: 'featureId',
  limitId: 'limitId',
  used: 'used',
  period: 'period',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.NotificationSettingsScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  emailEnabled: 'emailEnabled',
  emailDigest: 'emailDigest',
  emailFormat: 'emailFormat',
  jobMatchEnabled: 'jobMatchEnabled',
  jobMatchFrequency: 'jobMatchFrequency',
  applicationStatusEnabled: 'applicationStatusEnabled',
  newJobsEnabled: 'newJobsEnabled',
  newJobsFrequency: 'newJobsFrequency',
  interviewRemindersEnabled: 'interviewRemindersEnabled',
  savedJobsUpdatesEnabled: 'savedJobsUpdatesEnabled',
  automationEnabled: 'automationEnabled',
  automationFrequency: 'automationFrequency',
  jobEmailEnabled: 'jobEmailEnabled',
  jobBrowserEnabled: 'jobBrowserEnabled',
  jobMobileEnabled: 'jobMobileEnabled',
  marketingEnabled: 'marketingEnabled',
  productUpdatesEnabled: 'productUpdatesEnabled',
  newsletterEnabled: 'newsletterEnabled',
  eventInvitationsEnabled: 'eventInvitationsEnabled',
  browserEnabled: 'browserEnabled',
  desktopEnabled: 'desktopEnabled',
  mobileEnabled: 'mobileEnabled',
  pushEnabled: 'pushEnabled',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PushSubscriptionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  endpoint: 'endpoint',
  p256dh: 'p256dh',
  auth: 'auth',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  title: 'title',
  message: 'message',
  url: 'url',
  type: 'type',
  priority: 'priority',
  read: 'read',
  global: 'global',
  metadata: 'metadata',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DocumentSubmissionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  documentId: 'documentId',
  createdAt: 'createdAt'
};

exports.Prisma.ReferralScalarFieldEnum = {
  id: 'id',
  referrerId: 'referrerId',
  referredId: 'referredId',
  referralCode: 'referralCode',
  status: 'status',
  rewardType: 'rewardType',
  rewardAmount: 'rewardAmount',
  rewardGiven: 'rewardGiven',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  completedAt: 'completedAt'
};

exports.Prisma.ReferralCodeHistoryScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  referralCode: 'referralCode',
  isActive: 'isActive',
  createdAt: 'createdAt',
  deactivatedAt: 'deactivatedAt',
  reason: 'reason',
  metadata: 'metadata'
};

exports.Prisma.JobSearchScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  profileId: 'profileId',
  resumeId: 'resumeId',
  query: 'query',
  location: 'location',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.JobSearchResultScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  data: 'data',
  createdAt: 'createdAt'
};

exports.Prisma.PasswordResetTokenScalarFieldEnum = {
  id: 'id',
  email: 'email',
  token: 'token',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt'
};

exports.Prisma.JobAlertScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  name: 'name',
  searchParams: 'searchParams',
  frequency: 'frequency',
  enabled: 'enabled',
  lastSentAt: 'lastSentAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SavedJobScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  jobId: 'jobId',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AutomationRunScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  profileId: 'profileId',
  keywords: 'keywords',
  location: 'location',
  status: 'status',
  progress: 'progress',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  startedAt: 'startedAt',
  stoppedAt: 'stoppedAt',
  completedAt: 'completedAt',
  failedAt: 'failedAt',
  error: 'error',
  autoApplyEnabled: 'autoApplyEnabled',
  avgMatchScore: 'avgMatchScore',
  companySizePreference: 'companySizePreference',
  excludeCompanies: 'excludeCompanies',
  experienceLevelMax: 'experienceLevelMax',
  experienceLevelMin: 'experienceLevelMin',
  jobMatchData: 'jobMatchData',
  jobTypes: 'jobTypes',
  jobsApplied: 'jobsApplied',
  jobsFound: 'jobsFound',
  jobsSkipped: 'jobsSkipped',
  matchedJobIds: 'matchedJobIds',
  maxJobsToApply: 'maxJobsToApply',
  minMatchScore: 'minMatchScore',
  preferredCompanies: 'preferredCompanies',
  remotePreference: 'remotePreference',
  salaryMax: 'salaryMax',
  salaryMin: 'salaryMin',
  specifications: 'specifications'
};

exports.Prisma.CityScalarFieldEnum = {
  id: 'id',
  name: 'name',
  stateId: 'stateId',
  createdAt: 'createdAt'
};

exports.Prisma.CompanyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  domain: 'domain',
  website: 'website',
  logoUrl: 'logoUrl',
  overview: 'overview',
  social: 'social',
  headquartersCity: 'headquartersCity',
  headquarters_state_id: 'headquarters_state_id',
  headquarters_country_id: 'headquarters_country_id',
  companySize: 'companySize',
  companyStage: 'companyStage',
  founded: 'founded',
  jobCount: 'jobCount',
  activeJobCount: 'activeJobCount',
  aiRatingScore: 'aiRatingScore',
  aiAnalystNote: 'aiAnalystNote',
  createdAt: 'createdAt'
};

exports.Prisma.CountryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  isoCode: 'isoCode',
  createdAt: 'createdAt'
};

exports.Prisma.Job_collectionsScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  platform: 'platform',
  createdAt: 'createdAt'
};

exports.Prisma.Job_listingScalarFieldEnum = {
  id: 'id',
  platform: 'platform',
  jobId: 'jobId',
  title: 'title',
  company: 'company',
  location: 'location',
  url: 'url',
  isActive: 'isActive',
  isProcessing: 'isProcessing',
  createdAt: 'createdAt',
  lastCheckedAt: 'lastCheckedAt',
  employmentType: 'employmentType',
  remoteType: 'remoteType',
  experienceLevel: 'experienceLevel',
  description: 'description',
  postedDate: 'postedDate',
  closedAt: 'closedAt',
  applyLink: 'applyLink',
  benefits: 'benefits',
  requirements: 'requirements',
  salary: 'salary',
  salaryCurrency: 'salaryCurrency',
  salaryMax: 'salaryMax',
  salaryMin: 'salaryMin',
  securityClearance: 'securityClearance',
  skills: 'skills',
  travelRequired: 'travelRequired',
  updatedAt: 'updatedAt',
  yearsOfExperience: 'yearsOfExperience',
  experienceRequirements: 'experienceRequirements',
  companyId: 'companyId',
  stateId: 'stateId',
  isAnalyzed: 'isAnalyzed'
};

exports.Prisma.Job_match_resultScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  jobId: 'jobId',
  profileId: 'profileId',
  matchScore: 'matchScore',
  applied: 'applied',
  createdAt: 'createdAt'
};

exports.Prisma.LanguageScalarFieldEnum = {
  id: 'id',
  code: 'code',
  name: 'name',
  createdAt: 'createdAt'
};

exports.Prisma.OccupationScalarFieldEnum = {
  id: 'id',
  socCode: 'socCode',
  title: 'title',
  shortTitle: 'shortTitle',
  category: 'category',
  source: 'source',
  createdAt: 'createdAt'
};

exports.Prisma.SchoolScalarFieldEnum = {
  id: 'id',
  institution: 'institution',
  countryId: 'countryId',
  stateId: 'stateId',
  createdAt: 'createdAt'
};

exports.Prisma.SkillScalarFieldEnum = {
  id: 'id',
  name: 'name',
  type: 'type',
  source: 'source',
  createdAt: 'createdAt'
};

exports.Prisma.StateScalarFieldEnum = {
  id: 'id',
  name: 'name',
  code: 'code',
  countryId: 'countryId',
  createdAt: 'createdAt'
};

exports.Prisma.SearchJobScalarFieldEnum = {
  id: 'id',
  query: 'query',
  filters: 'filters',
  status: 'status',
  results: 'results',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  error: 'error'
};

exports.Prisma.WorkerProcessScalarFieldEnum = {
  id: 'id',
  type: 'type',
  status: 'status',
  data: 'data',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  error: 'error'
};

exports.Prisma.ScrapeProgressScalarFieldEnum = {
  id: 'id',
  type: 'type',
  lastCityIndex: 'lastCityIndex',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastOccupationIndex: 'lastOccupationIndex'
};

exports.Prisma.MaintenanceEventScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  startTime: 'startTime',
  endTime: 'endTime',
  status: 'status',
  severity: 'severity',
  affectedServices: 'affectedServices',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  notifiedAt: 'notifiedAt',
  completedAt: 'completedAt'
};

exports.Prisma.MaintenanceEventHistoryScalarFieldEnum = {
  id: 'id',
  eventId: 'eventId',
  userId: 'userId',
  changeType: 'changeType',
  previousStatus: 'previousStatus',
  newStatus: 'newStatus',
  comment: 'comment',
  metadata: 'metadata',
  createdAt: 'createdAt'
};

exports.Prisma.ServiceStatusScalarFieldEnum = {
  id: 'id',
  name: 'name',
  status: 'status',
  description: 'description',
  lastCheckedAt: 'lastCheckedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ServiceStatusHistoryScalarFieldEnum = {
  id: 'id',
  serviceId: 'serviceId',
  status: 'status',
  recordedAt: 'recordedAt',
  createdAt: 'createdAt'
};

exports.Prisma.JobMarketMetricsScalarFieldEnum = {
  id: 'id',
  occupationId: 'occupationId',
  level: 'level',
  remoteCount: 'remoteCount',
  totalCount: 'totalCount',
  avgSalary: 'avgSalary',
  salaryRange: 'salaryRange',
  topSkills: 'topSkills',
  topCompanies: 'topCompanies',
  collectedAt: 'collectedAt'
};

exports.Prisma.SkillTrendScalarFieldEnum = {
  id: 'id',
  skillName: 'skillName',
  category: 'category',
  occupationId: 'occupationId',
  mentionCount: 'mentionCount',
  growthRate: 'growthRate',
  avgSalaryImpact: 'avgSalaryImpact',
  collectedAt: 'collectedAt'
};

exports.Prisma.AtsAnalysisScalarFieldEnum = {
  id: 'id',
  resumeId: 'resumeId',
  overallScore: 'overallScore',
  keywordScore: 'keywordScore',
  formatScore: 'formatScore',
  contentScore: 'contentScore',
  readabilityScore: 'readabilityScore',
  detectedIssues: 'detectedIssues',
  suggestedKeywords: 'suggestedKeywords',
  analysisDetails: 'analysisDetails',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ParsedResumeScalarFieldEnum = {
  id: 'id',
  resumeId: 'resumeId',
  userId: 'userId',
  profileId: 'profileId',
  parsedAt: 'parsedAt',
  parserVersion: 'parserVersion',
  parserType: 'parserType',
  fileType: 'fileType',
  parseTime: 'parseTime',
  status: 'status',
  name: 'name',
  email: 'email',
  phone: 'phone',
  location: 'location',
  summary: 'summary',
  website: 'website',
  education: 'education',
  experience: 'experience',
  skills: 'skills',
  projects: 'projects',
  certifications: 'certifications',
  languages: 'languages',
  publications: 'publications',
  achievements: 'achievements',
  volunteer: 'volunteer',
  interests: 'interests',
  references: 'references',
  patents: 'patents',
  rawText: 'rawText',
  sectionMap: 'sectionMap',
  confidenceScores: 'confidenceScores',
  overallScore: 'overallScore'
};

exports.Prisma.InterviewCoachingSessionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  applicationId: 'applicationId',
  jobTitle: 'jobTitle',
  company: 'company',
  status: 'status',
  questions: 'questions',
  responses: 'responses',
  feedback: 'feedback',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ATSAnalysisScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  resumeId: 'resumeId',
  jobId: 'jobId',
  overallScore: 'overallScore',
  keywordScore: 'keywordScore',
  formatScore: 'formatScore',
  contentScore: 'contentScore',
  readabilityScore: 'readabilityScore',
  keywordMatches: 'keywordMatches',
  missingKeywords: 'missingKeywords',
  formatIssues: 'formatIssues',
  contentSuggestions: 'contentSuggestions',
  readabilitySuggestions: 'readabilitySuggestions',
  jobSpecific: 'jobSpecific',
  jobTitle: 'jobTitle',
  company: 'company',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.JobMatchAnalysisScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  profileId: 'profileId',
  jobId: 'jobId',
  overallMatchScore: 'overallMatchScore',
  skillsMatchScore: 'skillsMatchScore',
  experienceMatchScore: 'experienceMatchScore',
  educationMatchScore: 'educationMatchScore',
  keywordMatchScore: 'keywordMatchScore',
  matchedSkills: 'matchedSkills',
  missingSkills: 'missingSkills',
  strengthAreas: 'strengthAreas',
  improvementAreas: 'improvementAreas',
  recommendations: 'recommendations',
  jobTitle: 'jobTitle',
  company: 'company',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ResumeSuggestionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  resumeId: 'resumeId',
  section: 'section',
  originalText: 'originalText',
  suggestedText: 'suggestedText',
  reason: 'reason',
  applied: 'applied',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.JobStatsScalarFieldEnum = {
  id: 'id',
  jobType: 'jobType',
  itemsProcessed: 'itemsProcessed',
  success: 'success',
  durationMs: 'durationMs',
  details: 'details',
  createdAt: 'createdAt',
  startTime: 'startTime',
  endTime: 'endTime',
  error: 'error',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.CompanySize = exports.$Enums.CompanySize = {
  SIZE_1_10: 'SIZE_1_10',
  SIZE_11_50: 'SIZE_11_50',
  SIZE_51_200: 'SIZE_51_200',
  SIZE_201_500: 'SIZE_201_500',
  SIZE_501_1000: 'SIZE_501_1000',
  SIZE_1001_5000: 'SIZE_1001_5000',
  SIZE_5001_10000: 'SIZE_5001_10000',
  SIZE_10000_PLUS: 'SIZE_10000_PLUS'
};

exports.CompanyStage = exports.$Enums.CompanyStage = {
  BOOTSTRAPPED: 'BOOTSTRAPPED',
  PRE_SEED: 'PRE_SEED',
  SEED: 'SEED',
  SERIES_A: 'SERIES_A',
  SERIES_B: 'SERIES_B',
  SERIES_C: 'SERIES_C',
  PUBLIC: 'PUBLIC',
  ACQUIRED: 'ACQUIRED',
  ENTERPRISE: 'ENTERPRISE'
};

exports.Prisma.ModelName = {
  Team: 'Team',
  TeamMember: 'TeamMember',
  User: 'User',
  Account: 'Account',
  Session: 'Session',
  VerificationToken: 'VerificationToken',
  Application: 'Application',
  InterviewStage: 'InterviewStage',
  InterviewQuestion: 'InterviewQuestion',
  Profile: 'Profile',
  ProfileData: 'ProfileData',
  Document: 'Document',
  Resume: 'Resume',
  ResumeOptimization: 'ResumeOptimization',
  Subscription: 'Subscription',
  Plan: 'Plan',
  PlanFeature: 'PlanFeature',
  PlanFeatureLimit: 'PlanFeatureLimit',
  Feature: 'Feature',
  FeatureLimit: 'FeatureLimit',
  FeatureRequirement: 'FeatureRequirement',
  FeatureUsage: 'FeatureUsage',
  NotificationSettings: 'NotificationSettings',
  PushSubscription: 'PushSubscription',
  Notification: 'Notification',
  DocumentSubmission: 'DocumentSubmission',
  Referral: 'Referral',
  ReferralCodeHistory: 'ReferralCodeHistory',
  JobSearch: 'JobSearch',
  JobSearchResult: 'JobSearchResult',
  PasswordResetToken: 'PasswordResetToken',
  JobAlert: 'JobAlert',
  SavedJob: 'SavedJob',
  AutomationRun: 'AutomationRun',
  city: 'city',
  company: 'company',
  country: 'country',
  job_collections: 'job_collections',
  job_listing: 'job_listing',
  job_match_result: 'job_match_result',
  language: 'language',
  occupation: 'occupation',
  school: 'school',
  skill: 'skill',
  state: 'state',
  SearchJob: 'SearchJob',
  WorkerProcess: 'WorkerProcess',
  scrapeProgress: 'scrapeProgress',
  MaintenanceEvent: 'MaintenanceEvent',
  MaintenanceEventHistory: 'MaintenanceEventHistory',
  ServiceStatus: 'ServiceStatus',
  ServiceStatusHistory: 'ServiceStatusHistory',
  JobMarketMetrics: 'JobMarketMetrics',
  SkillTrend: 'SkillTrend',
  AtsAnalysis: 'AtsAnalysis',
  ParsedResume: 'ParsedResume',
  InterviewCoachingSession: 'InterviewCoachingSession',
  ATSAnalysis: 'ATSAnalysis',
  JobMatchAnalysis: 'JobMatchAnalysis',
  ResumeSuggestion: 'ResumeSuggestion',
  JobStats: 'JobStats'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
