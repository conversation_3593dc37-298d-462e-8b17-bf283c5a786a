<script lang="ts">
  import { toast } from 'svelte-sonner';
  import { writable } from 'svelte/store';
  import * as Sheet from '$lib/components/ui/sheet';
  import { Button } from '$lib/components/ui/button';
  import { Badge } from '$lib/components/ui/badge';
  import { Progress } from '$lib/components/ui/progress/index.js';
  import { Switch } from '$lib/components/ui/switch';
  import * as AlertDialog from '$lib/components/ui/alert-dialog';
  import * as Accordion from '$lib/components/ui/accordion';
  import { ScrollArea } from '$lib/components/ui/scroll-area';

  import { formatDistanceToNow } from '$lib/utils';
  import ResolvedKeywords from '$lib/components/automation/ResolvedKeywords.svelte';
  import ResolvedLocations from '$lib/components/automation/ResolvedLocations.svelte';
  import {
    Play,
    StopCircle,
    RefreshCw,
    FileText,
    Briefcase,
    CheckCircle,
    XCircle,
    Clock,
    Building,
    MapPin,
    DollarSign,
    Calendar,
    Target,
  } from 'lucide-svelte';

  // Props
  const {
    open = $bindable(false),
    automationRun,
    onClose = () => {},
    onRefresh = () => {},
    onStop = () => {},
  } = $props<{
    open?: boolean;
    automationRun: any;
    onClose?: () => void;
    onRefresh?: (updatedRun?: any) => void;
    onStop?: (runId?: string) => void;
  }>();

  // Local state
  let isLoading = $state(false);
  let isStoppingRun = $state(false);
  let selectedJobsForAutoApply = $state(new Set<string>());
  let showAutoApplyConfirm = $state(false);

  // State for real job data
  let jobListings = $state([]);
  let isLoadingJobs = $state(false);

  // Reactive automationRun store
  const runStore = writable(automationRun);
  $effect(() => {
    if (automationRun) {
      // Ensure autoApplyEnabled is false for testing switches
      runStore.set({
        ...automationRun,
        autoApplyEnabled: false,
      });
    }
  });

  // Function to fetch job listings for the automation run
  async function fetchJobListings() {
    if (
      !$runStore ||
      !$runStore.id ||
      !$runStore.matchedJobIds ||
      $runStore.matchedJobIds.length === 0
    ) {
      jobListings = [];
      return;
    }

    isLoadingJobs = true;
    try {
      const response = await fetch(`/api/automation/runs/${$runStore.id}/jobs`);
      if (response.ok) {
        const jobs = await response.json();
        jobListings = jobs.map((job: any) => ({
          ...job,
          matchScore: getJobMatchScore(job.id),
          postedAt: job.postedDate || job.createdAt,
        }));
      } else {
        console.error('Failed to fetch job listings');
        jobListings = [];
      }
    } catch (error) {
      console.error('Error fetching job listings:', error);
      jobListings = [];
    } finally {
      isLoadingJobs = false;
    }
  }

  // Function to get match score for a job from jobMatchData
  function getJobMatchScore(jobId: string): number {
    if (!$runStore?.jobMatchData) return 0;
    const matchData = $runStore.jobMatchData[jobId];
    return matchData?.matchScore || 0;
  }

  // Function to format salary display
  function formatSalary(job: any): string {
    if (job.salary) return job.salary;
    if (job.salaryMin && job.salaryMax) {
      const min = Math.round(job.salaryMin / 1000);
      const max = Math.round(job.salaryMax / 1000);
      return `$${min}k - $${max}k`;
    }
    if (job.salaryMin) {
      const min = Math.round(job.salaryMin / 1000);
      return `$${min}k+`;
    }
    return '';
  }

  // Use real job data
  const jobsToDisplay = $derived(jobListings);

  // Fetch jobs when automation run changes
  $effect(() => {
    if ($runStore && open) {
      fetchJobListings();
    }
  });

  // Function to toggle job selection for auto-apply
  function toggleJobSelection(jobId: string) {
    if (selectedJobsForAutoApply.has(jobId)) {
      selectedJobsForAutoApply.delete(jobId);
    } else {
      selectedJobsForAutoApply.add(jobId);
    }
    selectedJobsForAutoApply = new Set(selectedJobsForAutoApply);
  }

  // Function to show auto-apply confirmation
  function showAutoApplyConfirmation() {
    if (selectedJobsForAutoApply.size === 0) {
      toast.error('Please select at least one job to enable auto-apply');
      return;
    }
    showAutoApplyConfirm = true;
  }

  // Function to confirm and enable auto-apply
  async function confirmAutoApply() {
    if (!$runStore || !$runStore.id) return;

    try {
      const selectedJobs = Array.from(selectedJobsForAutoApply);
      console.log('Enabling auto-apply for jobs:', selectedJobs);

      // Update automation run settings via API
      const response = await fetch(`/api/automation/runs/${$runStore.id}/settings`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          autoApplyEnabled: true,
          selectedJobIds: selectedJobs,
        }),
      });

      if (response.ok) {
        const updatedRun = await response.json();
        runStore.set(updatedRun);

        toast.success(
          `Auto-apply enabled for ${selectedJobs.length} job${selectedJobs.length === 1 ? '' : 's'}`
        );
        showAutoApplyConfirm = false;
        selectedJobsForAutoApply.clear();
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to enable auto-apply');
      }
    } catch (error) {
      console.error('Error enabling auto-apply:', error);
      toast.error('Failed to enable auto-apply');
    }
  }

  // Function to stop an automation run
  async function stopAutomationRun() {
    if (!$runStore || !$runStore.id) return;

    isStoppingRun = true;

    try {
      const response = await fetch(`/api/automation/runs/${$runStore.id}/stop`, {
        method: 'POST',
      });

      if (response.ok) {
        const updatedRun = await response.json();
        runStore.update((run) => ({
          ...run,
          status: 'stopped',
          stoppedAt: updatedRun.stoppedAt,
        }));
        toast.success('Automation run stopped');
        onStop($runStore.id);
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to stop automation run');
      }
    } catch (error) {
      console.error('Error stopping automation run:', error);
      toast.error('An error occurred while stopping the automation run');
    } finally {
      isStoppingRun = false;
    }
  }

  // Function to refresh the automation run data
  async function refreshData() {
    if (!$runStore || !$runStore.id) return;

    isLoading = true;

    try {
      const response = await fetch(`/api/automation/runs/${$runStore.id}`);
      if (response.ok) {
        const updatedRun = await response.json();
        runStore.set(updatedRun);
        // Also refresh job listings
        await fetchJobListings();
        toast.success('Data refreshed');
        onRefresh(updatedRun);
      } else {
        toast.error('Failed to refresh data');
      }
    } catch (error) {
      console.error('Error refreshing data:', error);
      toast.error('An error occurred while refreshing data');
    } finally {
      isLoading = false;
    }
  }

  // Function to get status badge variant
  function getStatusVariant(status: string) {
    switch (status) {
      case 'start':
      case 'running':
        return 'default';
      case 'completed':
        return 'outline';
      case 'failed':
        return 'destructive';
      case 'stopped':
        return 'secondary';
      case 'in progress':
      case 'pending':
        return 'secondary';
      default:
        return 'secondary';
    }
  }

  // Function to get status icon
  function getStatusIcon(status: string) {
    switch (status) {
      case 'start':
      case 'running':
        return Play;
      case 'completed':
        return CheckCircle;
      case 'failed':
        return XCircle;
      case 'stopped':
        return StopCircle;
      case 'in progress':
      case 'pending':
        return Clock;
      default:
        return Clock;
    }
  }

  // Function to get dynamic status label
  function getStatusLabel(status: string) {
    switch (status) {
      case 'start':
        return 'Starting';
      case 'running':
        return 'Running';
      case 'completed':
        return 'Completed';
      case 'failed':
        return 'Failed';
      case 'stopped':
        return 'Stopped';
      case 'in progress':
        return 'In Progress';
      case 'pending':
        return 'Pending';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  }

  // Function to calculate progress percentage
  function calculateProgress(run: any) {
    if (!run) return 0;
    if (run.status === 'completed') return 100;
    if (['failed', 'stopped'].includes(run.status)) return run.progress || 0;
    if (run.status === 'start') return 5;
    if (run.status === 'in progress') return run.progress || 50;
    return run.progress || 0;
  }

  // Type definition for profile data
  type ProfileData = {
    fullName?: string;
    title?: string;
    headline?: string;
    skills?: string[];
  };

  // Helper function to safely access profile data
  function getProfileData(profile: any): ProfileData {
    if (!profile?.data) return {};

    try {
      // Try to parse the data if it's a string
      if (typeof profile.data === 'string') {
        return JSON.parse(profile.data);
      }

      // Otherwise, return the data object
      return profile.data as ProfileData;
    } catch (e) {
      console.error('Error parsing profile data:', e);
      return {};
    }
  }

  // Handle sheet close
  function handleSheetClose() {
    onClose();
  }
</script>

<Sheet.Root {open} onOpenChange={handleSheetClose}>
  <Sheet.Trigger />
  <Sheet.Portal>
    <Sheet.Overlay />
    <Sheet.Content side="right" class="w-full gap-0 sm:max-w-xl md:max-w-2xl lg:max-w-3xl">
      <Sheet.Header class="border-border m-0 flex flex-col gap-0 border-b">
        <Sheet.Title class="text-lg"
          >{getProfileData($runStore.profile).fullName || 'Unnamed Profile'}</Sheet.Title>
        <Sheet.Description>
          {getProfileData($runStore.profile).title ||
            getProfileData($runStore.profile).headline ||
            'No title specified'}</Sheet.Description>
      </Sheet.Header>
      <Progress value={calculateProgress($runStore)} max={100} class="mb-0 rounded-none" />
      <div class="flex flex-row justify-between p-4 pt-2">
        <div class="text-xs font-medium text-gray-400">Progress</div>
        <div class="text-right text-xs text-gray-400">
          {calculateProgress($runStore)}% Complete
        </div>
      </div>
      <ScrollArea class="h-[calc(100vh-100px)] w-full">
        {#if $runStore}
          <div class="space-y-6 px-4">
            <!-- Status and Actions -->
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <Badge variant={getStatusVariant($runStore.status)} class="text-sm">
                  {@const StatusIcon = getStatusIcon($runStore.status)}
                  <StatusIcon class="mr-1 h-3 w-3" />
                  {getStatusLabel($runStore.status)}
                </Badge>

                {#if $runStore.createdAt}
                  <span class="text-xs text-gray-400">
                    Started {formatDistanceToNow(new Date($runStore.createdAt))} ago
                  </span>
                {/if}
              </div>

              <div class="flex items-center gap-2">
                {#if ['running', 'pending', 'start', 'in progress'].includes($runStore.status)}
                  <Button
                    variant="outline"
                    size="sm"
                    onclick={stopAutomationRun}
                    disabled={isStoppingRun}>
                    <StopCircle class="mr-2 h-4 w-4" />
                    {isStoppingRun ? 'Stopping...' : 'Stop Run'}
                  </Button>
                {/if}

                <Button variant="outline" size="sm" onclick={refreshData} disabled={isLoading}>
                  <RefreshCw class="mr-2 h-4 w-4 {isLoading ? 'animate-spin' : ''}" />
                  {isLoading ? 'Refreshing...' : 'Refresh'}
                </Button>
              </div>
            </div>

            <!-- Run Information -->
            <Accordion.Root type="single" class="border-border w-full rounded-md border">
              <Accordion.Item value="run-info">
                <Accordion.Trigger class="p-4 text-left">
                  <h3 class="text-sm font-medium text-gray-400">Search Parameters</h3>
                </Accordion.Trigger>
                <Accordion.Content>
                  <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <!-- Profile Info -->
                    <div class="rounded-lg border p-4">
                      <h4 class="mb-2 text-sm font-medium text-gray-400">Profile</h4>
                      {#if $runStore.profile}
                        <div class="mb-1 text-base font-medium">
                          {getProfileData($runStore.profile).fullName || 'Unnamed Profile'}
                        </div>
                        <div class="mb-2 text-sm text-gray-400">
                          {getProfileData($runStore.profile).title ||
                            getProfileData($runStore.profile).headline ||
                            'No title specified'}
                        </div>

                        {#if $runStore.profile.documents && $runStore.profile.documents.length > 0}
                          <Badge variant="outline" class="text-xs">
                            <FileText class="mr-1 h-3 w-3" />
                            {$runStore.profile.documents.length}
                            {$runStore.profile.documents.length === 1 ? 'resume' : 'resumes'}
                          </Badge>
                        {/if}
                      {:else}
                        <div class="text-sm text-gray-400">Profile information not available</div>
                      {/if}
                    </div>

                    <!-- Search Parameters -->
                    <div class="rounded-lg border p-4">
                      <h4 class="mb-2 text-sm font-medium text-gray-400">Search Criteria</h4>
                      <div class="space-y-2">
                        <div>
                          <span class="text-xs text-gray-400">Keywords:</span>
                          <div class="text-sm">
                            <ResolvedKeywords
                              keywordIds={$runStore.keywords || ''}
                              fallback="None specified" />
                          </div>
                        </div>

                        <div>
                          <span class="text-xs text-gray-400">Location:</span>
                          <div class="text-sm">
                            <ResolvedLocations
                              locationIds={$runStore.location || ''}
                              fallback="None specified" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Accordion.Content>
              </Accordion.Item>
            </Accordion.Root>

            <!-- Jobs Found -->
            <div>
              <div class="mb-4 flex items-center justify-between">
                <h3 class="text-sm font-medium text-gray-400">
                  Jobs Found ({isLoadingJobs ? '...' : jobsToDisplay.length})
                </h3>
                {#if jobsToDisplay.length > 0 && !$runStore.autoApplyEnabled}
                  <Button
                    variant="outline"
                    size="sm"
                    onclick={showAutoApplyConfirmation}
                    disabled={selectedJobsForAutoApply.size === 0}
                    class="h-8 text-xs">
                    <Target class="mr-1 h-3 w-3" />
                    Auto Apply ({selectedJobsForAutoApply.size})
                  </Button>
                {/if}
              </div>

              {#if isLoadingJobs}
                <div
                  class="flex flex-col items-center justify-center rounded-lg border border-dashed p-6 text-center">
                  <RefreshCw class="mb-2 h-8 w-8 animate-spin text-gray-400" />
                  <p class="text-sm text-gray-400">Loading jobs...</p>
                </div>
              {:else if jobsToDisplay.length === 0}
                <div
                  class="flex flex-col items-center justify-center rounded-lg border border-dashed p-6 text-center">
                  <Briefcase class="mb-2 h-8 w-8 text-gray-400" />
                  <p class="text-sm text-gray-400">
                    {#if ['running', 'pending', 'start', 'in progress'].includes($runStore.status)}
                      Jobs will appear here as they are found
                    {:else}
                      No jobs were found during this automation run
                    {/if}
                  </p>
                </div>
              {:else}
                <div class="grid grid-cols-2 gap-4">
                  {#each jobsToDisplay as job (job.id)}
                    <div class="flex flex-col space-y-3 rounded-lg border p-4">
                      <!-- Job Header with Switch -->
                      <div class="flex items-start justify-between">
                        <div class="min-w-0 flex-1">
                          <div class="flex items-center gap-2">
                            <a href={job.applyLink} target="_blank" class="font-medium"
                              >{job.title}</a>
                            {#if $runStore.autoApplyEnabled && $runStore.selectedJobIds?.includes(job.id)}
                              <Badge variant="default" class="bg-blue-600 text-xs"
                                >Auto-Apply</Badge>
                            {/if}
                          </div>
                          {#if job.company}
                            <div class="flex items-center text-sm text-gray-400">
                              <Building class="mr-1 h-3 w-3" />
                              {job.company}
                            </div>
                          {/if}
                        </div>

                        <div class="flex items-center gap-2">
                          {#if !$runStore.autoApplyEnabled}
                            <Switch
                              checked={selectedJobsForAutoApply.has(job.id)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  toggleJobSelection(job.id);
                                } else {
                                  toggleJobSelection(job.id);
                                }
                              }} />
                          {/if}
                        </div>
                      </div>

                      <!-- Job Details -->
                      <div class="grid grid-cols-2 gap-2 text-xs">
                        {#if job.matchScore}
                          <Badge variant="outline" class="text-xs">
                            {job.matchScore}% match
                          </Badge>
                        {/if}

                        {#if job.location}
                          <div class="flex items-center text-gray-400">
                            <MapPin class="mr-1 h-3 w-3" />
                            {job.location}
                          </div>
                        {/if}

                        {#if formatSalary(job)}
                          {@const formattedSalary = formatSalary(job)}
                          <div class="flex items-center text-gray-400">
                            <DollarSign class="mr-1 h-3 w-3" />
                            {formattedSalary}
                          </div>
                        {/if}

                        {#if job.postedAt}
                          <div class="flex items-center text-gray-400">
                            <Calendar class="mr-1 h-3 w-3" />
                            Posted {formatDistanceToNow(new Date(job.postedAt))} ago
                          </div>
                        {/if}
                      </div>

                      <!-- Remote Type and Benefits -->
                      <div class="space-y-2">
                        {#if job.remoteType}
                          <div class="flex items-center gap-1">
                            <Badge variant="outline" class="text-xs">
                              {job.remoteType}
                            </Badge>
                          </div>
                        {/if}

                        {#if job.benefits && job.benefits.length > 0}
                          <div class="flex flex-wrap gap-1">
                            {#each job.benefits.slice(0, 2) as benefit}
                              <Badge variant="secondary" class="text-xs">{benefit}</Badge>
                            {/each}
                            {#if job.benefits.length > 2}
                              <Badge variant="secondary" class="text-xs"
                                >+{job.benefits.length - 2} more</Badge>
                            {/if}
                          </div>
                        {/if}
                      </div>
                    </div>
                  {/each}
                </div>
              {/if}
            </div>
          </div>
        {:else}
          <div class="flex h-40 items-center justify-center">
            <p class="text-gray-400">No automation run data available</p>
          </div>
        {/if}
      </ScrollArea>
      <Sheet.Footer
        class="border-border m-0 grid grid-cols-4 flex-col-reverse gap-4 border-t p-2 sm:flex-row sm:justify-end">
        <Button variant="outline" onclick={handleSheetClose}>Close</Button>
        <Button
          variant="default"
          onclick={() => {
            handleSheetClose();
            if ($runStore && $runStore.id) {
              window.location.href = `/dashboard/automation/${$runStore.id}`;
            }
          }}>
          View Full Details
        </Button>
      </Sheet.Footer>
    </Sheet.Content>
  </Sheet.Portal>
</Sheet.Root>

<!-- Auto-Apply Confirmation Dialog -->
<AlertDialog.Root bind:open={showAutoApplyConfirm}>
  <AlertDialog.Portal>
    <AlertDialog.Overlay />
    <AlertDialog.Content class="gap-0 p-0 sm:max-w-[425px]">
      <AlertDialog.Header class="border-border border-b p-4">
        <AlertDialog.Title>Confirm Auto-Apply</AlertDialog.Title>
      </AlertDialog.Header>
      <AlertDialog.Description class="p-4">
        Are you sure you want to enable auto-apply for {selectedJobsForAutoApply.size} selected job{selectedJobsForAutoApply.size ===
        1
          ? ''
          : 's'}?
        <br /><br />
        This will automatically submit applications to the selected jobs using your profile and resume.
      </AlertDialog.Description>

      <AlertDialog.Footer class="border-border flex justify-end gap-4 border-t p-2">
        <AlertDialog.Cancel onclick={() => (showAutoApplyConfirm = false)}
          >Cancel</AlertDialog.Cancel>
        <AlertDialog.Action onclick={confirmAutoApply}>
          <Target class="mr-2 h-4 w-4" />
          Enable Auto-Apply
        </AlertDialog.Action>
      </AlertDialog.Footer>
    </AlertDialog.Content>
  </AlertDialog.Portal>
</AlertDialog.Root>
