// src/lib/server/notification-service.ts
import { prisma } from '$lib/server/prisma';
import { getRedisClient } from '$lib/server/redis';
import { sendPushNotificationToUser } from '$lib/server/push-notifications';

// We no longer use in-memory notification storage
// All notifications should be stored in the database

// Notification types
export enum NotificationType {
  SYSTEM = 'system',
  JOB = 'job',
  APPLICATION = 'application',
  INTERVIEW = 'interview',
  MESSAGE = 'message',
  ERROR = 'error',
  SUCCESS = 'success',
  WARNING = 'warning',
  INFO = 'info',
}

// Notification priority
export enum NotificationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

// Notification data interface
export interface NotificationData {
  title: string;
  message: string;
  url?: string;
  type?: NotificationType;
  priority?: NotificationPriority;
  metadata?: Record<string, any>;
  expiresAt?: Date;
}

/**
 * Send a notification to a specific user
 */
export async function sendNotificationToUser(
  userId: string,
  data: NotificationData
): Promise<boolean> {
  try {
    // Get user notification preferences
    const preferences = await prisma.notificationSettings.findUnique({
      where: { userId },
    });

    // If user has disabled in-app notifications, don't send
    if (preferences && !preferences.browserEnabled) {
      console.log(`User ${userId} has disabled in-app notifications`);
      return false;
    }

    // Create notification data
    const notificationData = {
      userId,
      title: data.title,
      message: data.message,
      url: data.url,
      type: data.type || NotificationType.INFO,
      priority: data.priority || NotificationPriority.MEDIUM,
      metadata: data.metadata ? JSON.stringify(data.metadata) : null,
      expiresAt: data.expiresAt,
      read: false,
    };

    // Store notification in database
    let notification;
    try {
      notification = await prisma.notification.create({
        data: notificationData,
      });
      console.log(`Created notification in database with ID ${notification.id}`);
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error; // Re-throw the error to be handled by the caller
    }

    // Send notification via Redis
    const redis = await getRedisClient();
    if (!redis) {
      console.error('Redis client not available');
      return false;
    }

    // Generate a unique request ID for this notification
    const requestId = `notification:${notification.id}:${Date.now()}`;
    console.log(`Generated request ID: ${requestId} for notification ${notification.id}`);

    // Publish to user-specific channel with the request ID
    console.log(
      `Publishing notification ${notification.id} to Redis channel user:${userId}:notifications with request ID ${requestId}`
    );

    await redis.publish(
      `user:${userId}:notifications`,
      JSON.stringify({
        id: notification.id,
        title: notification.title,
        message: notification.message,
        url: notification.url,
        type: notification.type,
        timestamp: notification.createdAt.toISOString(),
        requestId: requestId, // Include the request ID in the notification
      })
    );

    // Also send push notification if user has it enabled
    try {
      await sendPushNotificationToUser(userId, {
        title: data.title,
        message: data.message,
        url: data.url,
        data: data.metadata,
      });
    } catch (pushError) {
      console.error('Error sending push notification:', pushError);
      // Don't fail the entire notification if push fails
    }

    return true;
  } catch (error) {
    console.error('Error sending notification to user:', error);
    return false;
  }
}

/**
 * Send a notification to multiple users
 */
export async function sendNotificationToUsers(
  userIds: string[],
  data: NotificationData
): Promise<{ success: number; failed: number }> {
  let success = 0;
  let failed = 0;

  for (const userId of userIds) {
    const result = await sendNotificationToUser(userId, data);
    if (result) {
      success++;
    } else {
      failed++;
    }
  }

  return { success, failed };
}

/**
 * Send a global notification to all users
 */
export async function sendGlobalNotification(data: NotificationData): Promise<boolean> {
  try {
    // Create notification data
    const notificationData = {
      title: data.title,
      message: data.message,
      url: data.url,
      type: data.type || NotificationType.SYSTEM,
      priority: data.priority || NotificationPriority.MEDIUM,
      metadata: data.metadata ? JSON.stringify(data.metadata) : null,
      expiresAt: data.expiresAt,
      global: true,
    };

    // Store notification in database as a global notification
    let notification;
    try {
      notification = await prisma.notification.create({
        data: notificationData,
      });
      console.log(`Created global notification in database with ID ${notification.id}`);
    } catch (error) {
      console.error('Error creating global notification:', error);
      throw error; // Re-throw the error to be handled by the caller
    }

    // Send notification via Redis
    const redis = await getRedisClient();
    if (!redis) {
      console.error('Redis client not available');
      return false;
    }

    // Generate a unique request ID for this notification
    const requestId = `notification:${notification.id}:${Date.now()}`;
    console.log(`Generated request ID: ${requestId} for global notification ${notification.id}`);

    // Publish to global channel with the request ID
    console.log(
      `Publishing global notification ${notification.id} to Redis channel global:notifications with request ID ${requestId}`
    );

    // Publish to global channel
    await redis.publish(
      'global:notifications',
      JSON.stringify({
        id: notification.id,
        title: notification.title,
        message: notification.message,
        url: notification.url,
        type: notification.type,
        timestamp: notification.createdAt.toISOString(),
        global: true,
        requestId: requestId, // Include the request ID in the notification
      })
    );

    return true;
  } catch (error) {
    console.error('Error sending global notification:', error);
    return false;
  }
}

/**
 * Send a job-related notification
 */
export async function sendJobNotification(
  userId: string,
  data: NotificationData
): Promise<boolean> {
  try {
    // Get user notification preferences
    const preferences = await prisma.notificationSettings.findUnique({
      where: { userId },
    });

    // If user has disabled job alerts, don't send
    if (preferences && !preferences.jobMatchEnabled) {
      console.log(`User ${userId} has disabled job alerts`);
      return false;
    }

    // Set notification type to JOB
    const jobData = {
      ...data,
      type: NotificationType.JOB,
    };

    return await sendNotificationToUser(userId, jobData);
  } catch (error) {
    console.error('Error sending job notification:', error);
    return false;
  }
}

/**
 * Send an application update notification
 */
export async function sendApplicationNotification(
  userId: string,
  data: NotificationData
): Promise<boolean> {
  try {
    // Get user notification preferences
    const preferences = await prisma.notificationSettings.findUnique({
      where: { userId },
    });

    // If user has disabled application updates, don't send
    if (preferences && !preferences.applicationStatusEnabled) {
      console.log(`User ${userId} has disabled application updates`);
      return false;
    }

    // Set notification type to APPLICATION
    const applicationData = {
      ...data,
      type: NotificationType.APPLICATION,
    };

    return await sendNotificationToUser(userId, applicationData);
  } catch (error) {
    console.error('Error sending application notification:', error);
    return false;
  }
}

/**
 * Mark a notification as read
 */
export async function markNotificationAsRead(notificationId: string): Promise<boolean> {
  try {
    // Update the notification in the database
    const notification = await prisma.notification.update({
      where: { id: notificationId },
      data: { read: true },
    });

    // Generate a unique request ID for this update
    const requestId = `notification_read:${notificationId}:${Date.now()}`;
    console.log(
      `Generated request ID: ${requestId} for marking notification ${notificationId} as read`
    );

    // Get Redis client to publish the update
    const redis = await getRedisClient();
    if (redis && notification.userId) {
      // Publish to user-specific channel to notify of the update
      console.log(
        `Publishing read status update for notification ${notificationId} to Redis channel user:${notification.userId}:notifications with request ID ${requestId}`
      );

      await redis.publish(
        `user:${notification.userId}:notifications`,
        JSON.stringify({
          type: 'notification_read',
          id: notificationId,
          timestamp: new Date().toISOString(),
          requestId: requestId, // Include the request ID
        })
      );
    }

    return true;
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return false;
  }
}

/**
 * Delete a notification
 */
export async function deleteNotification(notificationId: string): Promise<boolean> {
  try {
    await prisma.notification.delete({
      where: { id: notificationId },
    });
    return true;
  } catch (error) {
    console.error('Error deleting notification:', error);
    return false;
  }
}

/**
 * Get user notifications
 */
export async function getUserNotifications(
  userId: string,
  options: {
    limit?: number;
    offset?: number;
    includeRead?: boolean;
    type?: NotificationType;
  } = {}
): Promise<any[]> {
  const { limit = 50, offset = 0, includeRead = false, type } = options;

  try {
    // Build where clause with user filter
    const whereClause: any = {
      OR: [{ userId }, { global: true }],
    };

    // Filter by type if specified
    if (type) {
      whereClause.type = type;
    }

    // Filter by read status if needed
    if (!includeRead) {
      whereClause.read = false;
    }

    // Get notifications
    let notifications = [];
    try {
      notifications = await prisma.notification.findMany({
        where: whereClause,
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
      });

      console.log(`Found ${notifications.length} notifications in database`);
      if (notifications.length > 0) {
        console.log('First notification:', JSON.stringify(notifications[0]));
      }
    } catch (error) {
      console.error('Error getting user notifications from database:', error);
      // Return empty array on error
      return [];
    }

    // Format notifications to match the client-side Notification type
    return notifications.map((notification) => ({
      id: notification.id,
      title: notification.title,
      message: notification.message,
      url: notification.url,
      type: notification.type,
      read: notification.read,
      global: notification.global,
      createdAt: notification.createdAt,
      timestamp: notification.createdAt, // Add timestamp for backward compatibility
    }));
  } catch (error) {
    console.error('Error getting user notifications:', error);
    return [];
  }
}
