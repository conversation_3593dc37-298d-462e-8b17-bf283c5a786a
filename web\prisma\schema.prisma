generator client {
  provider        = "prisma-client-js"
  output          = "../node_modules/.prisma/client"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["cron", "web", "workers"]
}

model Team {
  id         String       @id @default(uuid())
  name       String
  ownerId    String
  createdAt  DateTime     @default(now())
  Document   Document[]
  Profile    Profile[]
  User       User         @relation(fields: [ownerId], references: [id])
  TeamMember TeamMember[]

  @@schema("web")
}

model TeamMember {
  id       String   @id @default(uuid())
  userId   String
  teamId   String
  role     String
  joinedAt DateTime @default(now())
  Team     Team     @relation(fields: [teamId], references: [id])
  User     User     @relation(fields: [userId], references: [id])

  @@unique([userId, teamId])
  @@schema("web")
}

model User {
  id                       String                     @id @default(uuid())
  email                    String                     @unique
  name                     String?
  image                    String?
  passwordHash             String?
  role                     String                     @default("free")
  seats                    Int                        @default(1)
  stripeCustomerId         String?                    @map("stripe_customer_id")
  createdAt                DateTime                   @default(now()) @map("created_at")
  updatedAt                DateTime                   @updatedAt @map("updated_at")
  preferences              Json?                      @default("{}")
  isAdmin                  Boolean                    @default(false)
  emailVerified            Boolean                    @default(false)
  verificationExpires      DateTime?
  verificationToken        String?
  provider                 String?
  providerId               String?
  referralCode             String?                    @unique
  referredById             String?
  referralCount            Int                        @default(0)
  referralRewards          Json?                      @default("{}")
  ATSAnalysis              ATSAnalysis[]
  Account                  Account[]
  Application              Application[]
  AutomationRun            AutomationRun[]
  Document                 Document[]
  DocumentSubmission       DocumentSubmission[]
  FeatureUsage             FeatureUsage[]
  InterviewCoachingSession InterviewCoachingSession[]
  JobAlert                 JobAlert[]
  JobMatchAnalysis         JobMatchAnalysis[]
  JobSearch                JobSearch[]
  JobSearchResult          JobSearchResult[]
  Notification             Notification[]
  NotificationSettings     NotificationSettings?
  Profile                  Profile[]
  pushSubscriptions        PushSubscription[]
  referralsReceived        Referral[]                 @relation("ReferralsToUser")
  referralsMade            Referral[]                 @relation("ReferralsFromUser")
  ReferralCodeHistory      ReferralCodeHistory[]
  ResumeSuggestion         ResumeSuggestion[]
  SavedJob                 SavedJob[]
  Session                  Session[]
  Subscription             Subscription[]
  Team                     Team[]
  TeamMember               TeamMember[]
  User                     User?                      @relation("UserToUser", fields: [referredById], references: [id])
  other_User               User[]                     @relation("UserToUser")

  @@schema("web")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  User              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@schema("web")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  browser      String?
  device       String?
  ip           String?
  isRevoked    Boolean  @default(false)
  lastActive   DateTime @default(now())
  location     String?
  os           String?
  userAgent    String?
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@schema("web")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@schema("web")
}

model Application {
  id             String           @id @default(uuid())
  userId         String
  company        String
  position       String
  location       String?
  appliedDate    DateTime
  status         String
  nextAction     String?
  notes          String?
  url            String?
  jobType        String?
  resumeUploaded Boolean          @default(false)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  User           User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  InterviewStage InterviewStage[]

  @@schema("web")
}

model InterviewStage {
  id                String              @id @default(uuid())
  applicationId     String
  stageName         String
  stageDate         DateTime
  outcome           String?
  feedback          String?
  interviewers      String?
  duration          Int?
  notes             String?
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  nextAction        String?
  InterviewQuestion InterviewQuestion[]
  Application       Application         @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@schema("web")
}

model InterviewQuestion {
  id               String         @id @default(uuid())
  interviewStageId String
  question         String
  category         String
  difficulty       Int?
  userResponse     String?
  userConfidence   Int?
  notes            String?
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt
  InterviewStage   InterviewStage @relation(fields: [interviewStageId], references: [id], onDelete: Cascade)

  @@schema("web")
}

model Profile {
  id                                           String             @id @default(uuid())
  name                                         String
  userId                                       String?
  teamId                                       String?
  createdAt                                    DateTime           @default(now())
  updatedAt                                    DateTime           @updatedAt
  defaultDocumentId                            String?            @unique
  AutomationRun                                AutomationRun[]
  Document_Document_profileIdToProfile         Document[]         @relation("Document_profileIdToProfile")
  JobMatchAnalysis                             JobMatchAnalysis[]
  JobSearch                                    JobSearch[]
  Document_Profile_defaultDocumentIdToDocument Document?          @relation("Profile_defaultDocumentIdToDocument", fields: [defaultDocumentId], references: [id])
  Team                                         Team?              @relation(fields: [teamId], references: [id])
  User                                         User?              @relation(fields: [userId], references: [id])
  ProfileData                                  ProfileData?

  @@schema("web")
}

model ProfileData {
  id        String   @id @default(uuid())
  profileId String   @unique
  data      Json
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  Profile   Profile  @relation(fields: [profileId], references: [id], onDelete: Cascade)

  @@schema("web")
}

model Document {
  id                                          String             @id @default(cuid())
  label                                       String
  fileUrl                                     String
  type                                        String             @default("resume")
  contentType                                 String?
  createdAt                                   DateTime           @default(now())
  updatedAt                                   DateTime           @updatedAt
  isDefault                                   Boolean            @default(false)
  profileId                                   String?
  userId                                      String
  teamId                                      String?
  fileSize                                    Int?
  pageCount                                   Int?
  fileName                                    String?
  filePath                                    String?
  storageLocation                             String?
  storageType                                 String             @default("local")
  ATSAnalysis                                 ATSAnalysis[]
  Profile_Document_profileIdToProfile         Profile?           @relation("Document_profileIdToProfile", fields: [profileId], references: [id])
  Team                                        Team?              @relation(fields: [teamId], references: [id])
  User                                        User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  Profile_Profile_defaultDocumentIdToDocument Profile?           @relation("Profile_defaultDocumentIdToDocument")
  Resume                                      Resume?
  ResumeSuggestion                            ResumeSuggestion[]

  @@schema("web")
}

model Resume {
  id                 String              @id @default(cuid())
  createdAt          DateTime            @default(now())
  documentId         String              @unique
  score              Float?
  updatedAt          DateTime            @updatedAt
  isParsed           Boolean             @default(false)
  parsedAt           DateTime?
  rawText            String?
  parsedData         Json?
  JobSearch          JobSearch[]
  Document           Document            @relation(fields: [documentId], references: [id], onDelete: Cascade)
  ResumeOptimization ResumeOptimization?

  @@schema("web")
}

model ResumeOptimization {
  id          String   @id @default(uuid())
  resumeId    String   @unique
  score       Float?
  summary     String?
  suggestions Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  Resume      Resume   @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@schema("web")
}

model Subscription {
  id                   String    @id @default(uuid())
  userId               String
  stripeSessionId      String?
  stripePriceId        String?
  planId               String?
  quantity             Int       @default(1)
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt
  cancelAtPeriodEnd    Boolean   @default(false)
  canceledAt           DateTime?
  currentPeriodEnd     DateTime?
  currentPeriodStart   DateTime?
  pausedAt             DateTime?
  resumeAt             DateTime?
  status               String    @default("active")
  stripeSubscriptionId String?
  Plan                 Plan?     @relation(fields: [planId], references: [id])
  User                 User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@schema("web")
}

model Plan {
  id                   String         @id @unique @default(uuid())
  name                 String
  description          String
  section              String
  monthlyPrice         Int
  annualPrice          Int
  stripePriceMonthlyId String?
  stripePriceYearlyId  String?
  popular              Boolean        @default(false)
  createdAt            DateTime       @default(now())
  updatedAt            DateTime       @updatedAt
  PlanFeature          PlanFeature[]
  Subscription         Subscription[]

  @@schema("web")
}

model PlanFeature {
  id               String             @id @default(uuid())
  planId           String
  featureId        String
  accessLevel      String
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  Plan             Plan               @relation(fields: [planId], references: [id], onDelete: Cascade)
  PlanFeatureLimit PlanFeatureLimit[]

  @@unique([planId, featureId])
  @@schema("web")
}

model PlanFeatureLimit {
  id            String      @id @default(uuid())
  planFeatureId String
  limitId       String
  value         String
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  PlanFeature   PlanFeature @relation(fields: [planFeatureId], references: [id], onDelete: Cascade)

  @@unique([planFeatureId, limitId])
  @@schema("web")
}

model Feature {
  id           String         @id @unique @default(uuid())
  name         String
  description  String
  category     String
  icon         String?
  beta         Boolean        @default(false)
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt
  FeatureLimit FeatureLimit[]
  FeatureUsage FeatureUsage[]

  @@schema("web")
}

model FeatureLimit {
  id           String         @id @unique @default(uuid())
  featureId    String
  name         String
  description  String
  defaultValue String
  type         String
  unit         String?
  resetDay     Int?
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt
  Feature      Feature        @relation(fields: [featureId], references: [id], onDelete: Cascade)
  FeatureUsage FeatureUsage[]

  @@schema("web")
}

model FeatureRequirement {
  id                String   @id @default(uuid())
  featureId         String
  requiredFeatureId String
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@unique([featureId, requiredFeatureId])
  @@schema("web")
}

model FeatureUsage {
  id           String       @id @default(uuid())
  userId       String
  featureId    String
  limitId      String
  used         Int          @default(0)
  period       String?
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt
  Feature      Feature      @relation(fields: [featureId], references: [id])
  FeatureLimit FeatureLimit @relation(fields: [limitId], references: [id])
  User         User         @relation(fields: [userId], references: [id])

  @@unique([userId, featureId, limitId, period])
  @@schema("web")
}

model NotificationSettings {
  id                        String   @id @default(uuid())
  userId                    String   @unique
  emailEnabled              Boolean  @default(true)
  emailDigest               String   @default("daily")
  emailFormat               String   @default("html")
  jobMatchEnabled           Boolean  @default(true)
  jobMatchFrequency         String   @default("daily")
  applicationStatusEnabled  Boolean  @default(true)
  newJobsEnabled            Boolean  @default(true)
  newJobsFrequency          String   @default("daily")
  interviewRemindersEnabled Boolean  @default(true)
  savedJobsUpdatesEnabled   Boolean  @default(true)
  jobEmailEnabled           Boolean  @default(true)
  jobBrowserEnabled         Boolean  @default(true)
  jobMobileEnabled          Boolean  @default(false)
  marketingEnabled          Boolean  @default(true)
  productUpdatesEnabled     Boolean  @default(true)
  newsletterEnabled         Boolean  @default(false)
  eventInvitationsEnabled   Boolean  @default(false)
  browserEnabled            Boolean  @default(true)
  desktopEnabled            Boolean  @default(false)
  mobileEnabled             Boolean  @default(false)
  pushEnabled               Boolean  @default(true)
  createdAt                 DateTime @default(now())
  updatedAt                 DateTime @updatedAt
  automationEnabled         Boolean  @default(true)
  automationFrequency       String   @default("realtime")
  User                      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@schema("web")
}

model PushSubscription {
  id        String   @id @default(uuid())
  userId    String
  endpoint  String
  p256dh    String?
  auth      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, endpoint])
  @@schema("web")
}

model Notification {
  id        String    @id @default(uuid())
  userId    String?
  title     String
  message   String
  url       String?
  type      String    @default("info")
  priority  String    @default("medium")
  read      Boolean   @default(false)
  global    Boolean   @default(false)
  metadata  String?
  expiresAt DateTime?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  User      User?     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@schema("web")
}

model DocumentSubmission {
  id         String   @id @default(uuid())
  userId     String
  documentId String?
  createdAt  DateTime @default(now())
  User       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@schema("web")
}

model Referral {
  id           String    @id @default(uuid())
  referrerId   String
  referredId   String
  referralCode String
  status       String    @default("pending")
  rewardType   String?
  rewardAmount Float?
  rewardGiven  Boolean   @default(false)
  metadata     Json?     @default("{}")
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  completedAt  DateTime?
  referred     User      @relation("ReferralsToUser", fields: [referredId], references: [id], onDelete: Cascade)
  referrer     User      @relation("ReferralsFromUser", fields: [referrerId], references: [id], onDelete: Cascade)

  @@unique([referrerId, referredId])
  @@index([referralCode])
  @@index([status])
  @@schema("web")
}

model ReferralCodeHistory {
  id            String    @id @default(uuid())
  userId        String
  referralCode  String    @unique
  isActive      Boolean   @default(false)
  createdAt     DateTime  @default(now())
  deactivatedAt DateTime?
  reason        String?
  metadata      Json?     @default("{}")
  User          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([isActive])
  @@index([referralCode])
  @@index([userId])
  @@schema("web")
}

model JobSearch {
  id        String   @id @default(uuid())
  userId    String
  profileId String?
  resumeId  String?
  query     String
  location  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  Profile   Profile? @relation(fields: [profileId], references: [id])
  Resume    Resume?  @relation(fields: [resumeId], references: [id])
  User      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@schema("web")
}

model JobSearchResult {
  id        String   @id @default(uuid())
  userId    String
  data      Json
  createdAt DateTime @default(now())
  User      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@schema("web")
}

model PasswordResetToken {
  id        String   @id @default(uuid())
  email     String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  @@schema("web")
}

model JobAlert {
  id           String    @id @default(uuid())
  userId       String
  name         String
  searchParams Json
  frequency    String    @default("daily")
  enabled      Boolean   @default(true)
  lastSentAt   DateTime?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  User         User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@schema("web")
}

model SavedJob {
  id        String   @id @default(uuid())
  userId    String
  jobId     String
  notes     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  User      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, jobId])
  @@schema("web")
}

model AutomationRun {
  id                    String    @id @default(uuid())
  userId                String
  profileId             String
  keywords              String?
  location              String?
  status                String    @default("pending")
  progress              Float     @default(0)
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  startedAt             DateTime?
  stoppedAt             DateTime?
  completedAt           DateTime?
  failedAt              DateTime?
  error                 String?
  autoApplyEnabled      Boolean   @default(false)
  avgMatchScore         Float?
  companySizePreference String[]  @default([])
  excludeCompanies      String[]  @default([])
  experienceLevelMax    Int?
  experienceLevelMin    Int?
  jobMatchData          Json?
  jobTypes              String[]  @default([])
  jobsApplied           Int       @default(0)
  jobsFound             Int       @default(0)
  jobsSkipped           Int       @default(0)
  matchedJobIds         String[]  @default([])
  maxJobsToApply        Int?      @default(10)
  minMatchScore         Float?    @default(70)
  preferredCompanies    String[]  @default([])
  remotePreference      String?
  salaryMax             Int?
  salaryMin             Int?
  specifications        Json?
  Profile               Profile   @relation(fields: [profileId], references: [id])
  User                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@schema("web")
}

model city {
  id        String   @id
  name      String
  stateId   String
  createdAt DateTime @default(now())
  state     state    @relation(fields: [stateId], references: [id], onDelete: Cascade)

  @@unique([name, stateId])
  @@index([stateId])
  @@schema("cron")
}

model company {
  id                      String        @id
  name                    String        @unique
  domain                  String?       @unique
  website                 String?
  logoUrl                 String?
  overview                String?
  social                  Json?
  headquartersCity        String?
  headquarters_state_id   String?
  headquarters_country_id String?
  companySize             CompanySize?
  companyStage            CompanyStage?
  founded                 Int?
  jobCount                Int?
  activeJobCount          Int?
  aiRatingScore           Float?
  aiAnalystNote           Json?
  createdAt               DateTime      @default(now())
  country                 country?      @relation(fields: [headquarters_country_id], references: [id])
  state                   state?        @relation(fields: [headquarters_state_id], references: [id])
  job_listing             job_listing[]

  @@schema("cron")
}

model country {
  id        String    @id
  name      String    @unique
  isoCode   String?   @unique
  createdAt DateTime  @default(now())
  company   company[]
  school    school[]
  state     state[]

  @@schema("cron")
}

model job_collections {
  id        String   @id
  name      String   @unique
  slug      String   @unique
  platform  String
  createdAt DateTime @default(now())

  @@schema("cron")
}

model job_listing {
  id                     String             @id
  platform               String
  jobId                  String
  title                  String
  location               String
  url                    String             @unique
  isActive               Boolean            @default(true)
  isProcessing           Boolean            @default(false)
  createdAt              DateTime           @default(now())
  lastCheckedAt          DateTime
  employmentType         String?
  remoteType             String?
  experienceLevel        String?
  description            String?
  postedDate             DateTime?
  closedAt               DateTime?
  applyLink              String?
  benefits               String[]           @default([])
  requirements           String[]           @default([])
  salary                 String?
  salaryCurrency         String?
  salaryMax              Float?
  salaryMin              Float?
  securityClearance      String?
  skills                 String[]           @default([])
  travelRequired         Boolean            @default(false)
  updatedAt              DateTime           @default(now())
  yearsOfExperience      Int                @default(0)
  experienceRequirements Json?
  companyId              String?
  stateId                String?
  isAnalyzed             Boolean            @default(false)
  company                company?           @relation(fields: [companyId], references: [id])
  state                  state?             @relation(fields: [stateId], references: [id])
  job_match_result       job_match_result[]

  @@schema("cron")
}

model job_match_result {
  id          String      @id
  userId      String
  jobId       String
  profileId   String
  matchScore  Float
  applied     Boolean     @default(false)
  createdAt   DateTime    @default(now())
  job_listing job_listing @relation(fields: [jobId], references: [id])

  @@unique([userId, jobId, profileId])
  @@schema("cron")
}

model language {
  id        String   @id
  code      String   @unique
  name      String
  createdAt DateTime @default(now())

  @@schema("cron")
}

model occupation {
  id               String             @id
  socCode          String
  title            String
  shortTitle       String?
  category         String
  source           String?
  createdAt        DateTime           @default(now())
  JobMarketMetrics JobMarketMetrics[]
  SkillTrend       SkillTrend[]

  @@unique([socCode, title])
  @@schema("cron")
}

model school {
  id          String   @id
  institution String
  countryId   String?
  stateId     String?
  createdAt   DateTime @default(now())
  country     country? @relation(fields: [countryId], references: [id])
  state       state?   @relation(fields: [stateId], references: [id])

  @@unique([institution, stateId, countryId])
  @@schema("cron")
}

model skill {
  id        String   @id
  name      String   @unique
  type      String?
  source    String
  createdAt DateTime @default(now())

  @@schema("cron")
}

model state {
  id          String        @id
  name        String
  code        String?
  countryId   String
  createdAt   DateTime      @default(now())
  city        city[]
  company     company[]
  job_listing job_listing[]
  school      school[]
  country     country       @relation(fields: [countryId], references: [id], onDelete: Cascade)

  @@unique([countryId, code])
  @@unique([countryId, name])
  @@schema("cron")
}

model SearchJob {
  id          String    @id
  query       String
  filters     Json?
  status      String
  results     Json?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime
  startedAt   DateTime?
  completedAt DateTime?
  error       String?

  @@schema("workers")
}

model WorkerProcess {
  id          String    @id
  type        String
  status      String
  data        Json?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime
  startedAt   DateTime?
  completedAt DateTime?
  error       String?

  @@schema("workers")
}

model scrapeProgress {
  id                  String   @id
  type                String   @unique
  lastCityIndex       Int?
  metadata            String?
  createdAt           DateTime @default(now())
  updatedAt           DateTime @default(now())
  lastOccupationIndex Int?

  @@index([lastOccupationIndex])
  @@schema("cron")
}

model MaintenanceEvent {
  id                      String                    @id @default(uuid())
  title                   String
  description             String
  startTime               DateTime
  endTime                 DateTime
  status                  String
  severity                String
  affectedServices        String[]
  createdBy               String
  createdAt               DateTime                  @default(now())
  updatedAt               DateTime                  @updatedAt
  notifiedAt              DateTime?
  completedAt             DateTime?
  MaintenanceEventHistory MaintenanceEventHistory[]

  @@schema("web")
}

model MaintenanceEventHistory {
  id               String           @id @default(uuid())
  eventId          String
  userId           String
  changeType       String
  previousStatus   String?
  newStatus        String?
  comment          String?
  metadata         Json?
  createdAt        DateTime         @default(now())
  MaintenanceEvent MaintenanceEvent @relation(fields: [eventId], references: [id], onDelete: Cascade)

  @@index([createdAt])
  @@index([eventId])
  @@index([userId])
  @@schema("web")
}

model ServiceStatus {
  id                   String                 @id @default(uuid())
  name                 String                 @unique
  status               String
  description          String?
  lastCheckedAt        DateTime               @default(now())
  createdAt            DateTime               @default(now())
  updatedAt            DateTime               @updatedAt
  ServiceStatusHistory ServiceStatusHistory[]

  @@schema("web")
}

model ServiceStatusHistory {
  id            String        @id @default(uuid())
  serviceId     String
  status        String
  recordedAt    DateTime      @default(now())
  createdAt     DateTime      @default(now())
  ServiceStatus ServiceStatus @relation(fields: [serviceId], references: [id], onDelete: Cascade)

  @@schema("web")
}

model JobMarketMetrics {
  id           String     @id
  occupationId String
  level        String?
  remoteCount  Int
  totalCount   Int
  avgSalary    Float?
  salaryRange  Json?
  topSkills    Json?
  topCompanies Json?
  collectedAt  DateTime   @default(now())
  occupation   occupation @relation(fields: [occupationId], references: [id])

  @@unique([occupationId, level, collectedAt])
  @@schema("cron")
}

model SkillTrend {
  id              String      @id
  skillName       String
  category        String?
  occupationId    String?
  mentionCount    Int
  growthRate      Float?
  avgSalaryImpact Float?
  collectedAt     DateTime    @default(now())
  occupation      occupation? @relation(fields: [occupationId], references: [id])

  @@unique([skillName, occupationId, collectedAt])
  @@schema("cron")
}

model AtsAnalysis {
  id                String   @id
  resumeId          String   @unique
  overallScore      Float
  keywordScore      Float
  formatScore       Float
  contentScore      Float
  readabilityScore  Float
  detectedIssues    Json
  suggestedKeywords Json
  analysisDetails   Json?
  createdAt         DateTime @default(now())
  updatedAt         DateTime

  @@schema("workers")
}

model ParsedResume {
  id               String   @id
  resumeId         String   @unique
  userId           String
  profileId        String?
  parsedAt         DateTime @default(now())
  parserVersion    String
  parserType       String   @default("enhanced")
  fileType         String?
  parseTime        Int?
  status           String   @default("success")
  name             String?
  email            String?
  phone            String?
  location         String?
  summary          String?
  website          String?
  education        Json?
  experience       Json?
  skills           Json?
  projects         Json?
  certifications   Json?
  languages        Json?
  publications     Json?
  achievements     Json?
  volunteer        Json?
  interests        Json?
  references       Json?
  patents          Json?
  rawText          String?
  sectionMap       Json?
  confidenceScores Json?
  overallScore     Float?

  @@schema("workers")
}

model InterviewCoachingSession {
  id            String   @id @default(uuid())
  userId        String
  applicationId String?
  jobTitle      String
  company       String?
  status        String
  questions     Json
  responses     Json?
  feedback      Json?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  User          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@schema("web")
}

model ATSAnalysis {
  id                     String   @id @default(uuid())
  userId                 String
  resumeId               String
  jobId                  String?
  overallScore           Float
  keywordScore           Float
  formatScore            Float
  contentScore           Float
  readabilityScore       Float
  keywordMatches         String[]
  missingKeywords        String[]
  formatIssues           String[]
  contentSuggestions     String[]
  readabilitySuggestions String[]
  jobSpecific            Boolean  @default(false)
  jobTitle               String?
  company                String?
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt
  Document               Document @relation(fields: [resumeId], references: [id], onDelete: Cascade)
  User                   User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([resumeId, jobId])
  @@index([userId, resumeId])
  @@schema("web")
}

model JobMatchAnalysis {
  id                   String   @id @default(uuid())
  userId               String
  profileId            String
  jobId                String
  overallMatchScore    Float
  skillsMatchScore     Float
  experienceMatchScore Float
  educationMatchScore  Float
  keywordMatchScore    Float
  matchedSkills        String[]
  missingSkills        String[]
  strengthAreas        String[]
  improvementAreas     String[]
  recommendations      String[]
  jobTitle             String
  company              String
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
  Profile              Profile  @relation(fields: [profileId], references: [id], onDelete: Cascade)
  User                 User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([profileId, jobId])
  @@index([userId])
  @@schema("web")
}

model ResumeSuggestion {
  id            String   @id @default(uuid())
  userId        String
  resumeId      String
  section       String
  originalText  String
  suggestedText String
  reason        String
  applied       Boolean  @default(false)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  Document      Document @relation(fields: [resumeId], references: [id], onDelete: Cascade)
  User          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, resumeId])
  @@schema("web")
}

model JobStats {
  id             String    @id
  jobType        String
  itemsProcessed Int       @default(0)
  success        Boolean   @default(false)
  durationMs     Int       @default(0)
  details        Json?
  createdAt      DateTime  @default(now())
  startTime      DateTime  @default(now())
  endTime        DateTime?
  error          String?
  updatedAt      DateTime

  @@index([createdAt])
  @@index([endTime])
  @@index([jobType])
  @@schema("cron")
}

enum CompanySize {
  SIZE_1_10
  SIZE_11_50
  SIZE_51_200
  SIZE_201_500
  SIZE_501_1000
  SIZE_1001_5000
  SIZE_5001_10000
  SIZE_10000_PLUS

  @@schema("cron")
}

enum CompanyStage {
  BOOTSTRAPPED
  PRE_SEED
  SEED
  SERIES_A
  SERIES_B
  SERIES_C
  PUBLIC
  ACQUIRED
  ENTERPRISE

  @@schema("cron")
}
