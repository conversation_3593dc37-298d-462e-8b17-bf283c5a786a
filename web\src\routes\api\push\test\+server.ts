import { json } from '@sveltejs/kit';
import { getUserFromToken } from '$lib/server/auth';
import { sendTestPushNotification } from '$lib/server/push-notifications';
import type { RequestHandler } from './$types';

/**
 * Send a test push notification to the authenticated user
 */
export const POST: RequestHandler = async ({ cookies }) => {
  try {
    // Authenticate the user
    const user = await getUserFromToken(cookies);
    if (!user) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Send test push notification
    const success = await sendTestPushNotification(user.id);

    if (success) {
      return json({ 
        success: true, 
        message: 'Test push notification sent successfully' 
      });
    } else {
      return json({ 
        success: false, 
        message: 'Failed to send test push notification. Make sure push notifications are enabled and you have subscribed.' 
      }, { status: 400 });
    }
  } catch (error) {
    console.error('Error sending test push notification:', error);
    return json({ error: 'Failed to send test notification' }, { status: 500 });
  }
};
