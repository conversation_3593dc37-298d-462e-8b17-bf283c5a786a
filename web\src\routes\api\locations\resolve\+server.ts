import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import type { RequestHandler } from './$types';

// Resolve city IDs to their names and states
export const POST: RequestHandler = async ({ request }) => {
  try {
    const { ids } = await request.json();
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return json([]);
    }

    const cities = await prisma.city.findMany({
      where: {
        id: {
          in: ids,
        },
      },
      include: {
        state: {
          include: {
            country: true,
          },
        },
      },
    });

    return json(cities);
  } catch (error) {
    console.error('Error resolving city IDs:', error);
    return json({ error: 'Failed to resolve city IDs' }, { status: 500 });
  }
};
