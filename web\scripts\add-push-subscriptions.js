"use strict";
var __makeTemplateObject = (this && this.__makeTemplateObject) || function (cooked, raw) {
    if (Object.defineProperty) { Object.defineProperty(cooked, "raw", { value: raw }); } else { cooked.raw = raw; }
    return cooked;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var client_1 = require("@prisma/client");
var prisma = new client_1.PrismaClient();
function addPushSubscriptionsTable() {
    return __awaiter(this, void 0, void 0, function () {
        var existingTables, notificationSettingsExists, automationColumnExists, pushTableExists, error_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 17, 18, 20]);
                    console.log('🚀 Starting database migration...');
                    return [4 /*yield*/, prisma.$queryRaw(templateObject_1 || (templateObject_1 = __makeTemplateObject(["\n      SELECT table_name\n      FROM information_schema.tables\n      WHERE table_schema = 'web'\n      ORDER BY table_name;\n    "], ["\n      SELECT table_name\n      FROM information_schema.tables\n      WHERE table_schema = 'web'\n      ORDER BY table_name;\n    "])))];
                case 1:
                    existingTables = _a.sent();
                    console.log('📋 Existing tables:', existingTables);
                    return [4 /*yield*/, prisma.$queryRaw(templateObject_2 || (templateObject_2 = __makeTemplateObject(["\n      SELECT EXISTS (\n        SELECT FROM information_schema.tables\n        WHERE table_schema = 'web'\n        AND table_name = 'NotificationSettings'\n      );\n    "], ["\n      SELECT EXISTS (\n        SELECT FROM information_schema.tables\n        WHERE table_schema = 'web'\n        AND table_name = 'NotificationSettings'\n      );\n    "])))];
                case 2:
                    notificationSettingsExists = _a.sent();
                    if (!!notificationSettingsExists[0].exists) return [3 /*break*/, 6];
                    console.log('➕ Creating NotificationSettings table...');
                    return [4 /*yield*/, prisma.$executeRaw(templateObject_3 || (templateObject_3 = __makeTemplateObject(["\n        CREATE TABLE \"web\".\"NotificationSettings\" (\n          \"id\" TEXT NOT NULL,\n          \"userId\" TEXT NOT NULL,\n          \"emailEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"emailDigest\" TEXT NOT NULL DEFAULT 'daily',\n          \"emailFormat\" TEXT NOT NULL DEFAULT 'html',\n          \"jobMatchEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"jobMatchFrequency\" TEXT NOT NULL DEFAULT 'daily',\n          \"applicationStatusEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"newJobsEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"newJobsFrequency\" TEXT NOT NULL DEFAULT 'daily',\n          \"interviewRemindersEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"savedJobsUpdatesEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"automationEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"automationFrequency\" TEXT NOT NULL DEFAULT 'realtime',\n          \"jobEmailEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"jobBrowserEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"jobMobileEnabled\" BOOLEAN NOT NULL DEFAULT false,\n          \"marketingEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"productUpdatesEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"newsletterEnabled\" BOOLEAN NOT NULL DEFAULT false,\n          \"eventInvitationsEnabled\" BOOLEAN NOT NULL DEFAULT false,\n          \"browserEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"desktopEnabled\" BOOLEAN NOT NULL DEFAULT false,\n          \"mobileEnabled\" BOOLEAN NOT NULL DEFAULT false,\n          \"pushEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"createdAt\" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,\n          \"updatedAt\" TIMESTAMP(3) NOT NULL,\n\n          CONSTRAINT \"NotificationSettings_pkey\" PRIMARY KEY (\"id\")\n        );\n      "], ["\n        CREATE TABLE \"web\".\"NotificationSettings\" (\n          \"id\" TEXT NOT NULL,\n          \"userId\" TEXT NOT NULL,\n          \"emailEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"emailDigest\" TEXT NOT NULL DEFAULT 'daily',\n          \"emailFormat\" TEXT NOT NULL DEFAULT 'html',\n          \"jobMatchEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"jobMatchFrequency\" TEXT NOT NULL DEFAULT 'daily',\n          \"applicationStatusEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"newJobsEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"newJobsFrequency\" TEXT NOT NULL DEFAULT 'daily',\n          \"interviewRemindersEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"savedJobsUpdatesEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"automationEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"automationFrequency\" TEXT NOT NULL DEFAULT 'realtime',\n          \"jobEmailEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"jobBrowserEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"jobMobileEnabled\" BOOLEAN NOT NULL DEFAULT false,\n          \"marketingEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"productUpdatesEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"newsletterEnabled\" BOOLEAN NOT NULL DEFAULT false,\n          \"eventInvitationsEnabled\" BOOLEAN NOT NULL DEFAULT false,\n          \"browserEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"desktopEnabled\" BOOLEAN NOT NULL DEFAULT false,\n          \"mobileEnabled\" BOOLEAN NOT NULL DEFAULT false,\n          \"pushEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          \"createdAt\" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,\n          \"updatedAt\" TIMESTAMP(3) NOT NULL,\n\n          CONSTRAINT \"NotificationSettings_pkey\" PRIMARY KEY (\"id\")\n        );\n      "])))];
                case 3:
                    _a.sent();
                    // Add unique constraint and foreign key
                    return [4 /*yield*/, prisma.$executeRaw(templateObject_4 || (templateObject_4 = __makeTemplateObject(["\n        CREATE UNIQUE INDEX \"NotificationSettings_userId_key\" ON \"web\".\"NotificationSettings\"(\"userId\");\n      "], ["\n        CREATE UNIQUE INDEX \"NotificationSettings_userId_key\" ON \"web\".\"NotificationSettings\"(\"userId\");\n      "])))];
                case 4:
                    // Add unique constraint and foreign key
                    _a.sent();
                    return [4 /*yield*/, prisma.$executeRaw(templateObject_5 || (templateObject_5 = __makeTemplateObject(["\n        ALTER TABLE \"web\".\"NotificationSettings\" ADD CONSTRAINT \"NotificationSettings_userId_fkey\" FOREIGN KEY (\"userId\") REFERENCES \"web\".\"User\"(\"id\") ON DELETE CASCADE ON UPDATE CASCADE;\n      "], ["\n        ALTER TABLE \"web\".\"NotificationSettings\" ADD CONSTRAINT \"NotificationSettings_userId_fkey\" FOREIGN KEY (\"userId\") REFERENCES \"web\".\"User\"(\"id\") ON DELETE CASCADE ON UPDATE CASCADE;\n      "])))];
                case 5:
                    _a.sent();
                    console.log('✅ NotificationSettings table created successfully');
                    return [3 /*break*/, 10];
                case 6:
                    console.log('✅ NotificationSettings table already exists');
                    return [4 /*yield*/, prisma.$queryRaw(templateObject_6 || (templateObject_6 = __makeTemplateObject(["\n        SELECT EXISTS (\n          SELECT FROM information_schema.columns\n          WHERE table_schema = 'web'\n          AND table_name = 'NotificationSettings'\n          AND column_name = 'automationEnabled'\n        );\n      "], ["\n        SELECT EXISTS (\n          SELECT FROM information_schema.columns\n          WHERE table_schema = 'web'\n          AND table_name = 'NotificationSettings'\n          AND column_name = 'automationEnabled'\n        );\n      "])))];
                case 7:
                    automationColumnExists = _a.sent();
                    if (!!automationColumnExists[0].exists) return [3 /*break*/, 9];
                    console.log('➕ Adding automation fields to NotificationSettings...');
                    return [4 /*yield*/, prisma.$executeRaw(templateObject_7 || (templateObject_7 = __makeTemplateObject(["\n          ALTER TABLE \"web\".\"NotificationSettings\"\n          ADD COLUMN \"automationEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          ADD COLUMN \"automationFrequency\" TEXT NOT NULL DEFAULT 'realtime';\n        "], ["\n          ALTER TABLE \"web\".\"NotificationSettings\"\n          ADD COLUMN \"automationEnabled\" BOOLEAN NOT NULL DEFAULT true,\n          ADD COLUMN \"automationFrequency\" TEXT NOT NULL DEFAULT 'realtime';\n        "])))];
                case 8:
                    _a.sent();
                    console.log('✅ Automation fields added to NotificationSettings');
                    return [3 /*break*/, 10];
                case 9:
                    console.log('✅ Automation fields already exist in NotificationSettings');
                    _a.label = 10;
                case 10: return [4 /*yield*/, prisma.$queryRaw(templateObject_8 || (templateObject_8 = __makeTemplateObject(["\n      SELECT EXISTS (\n        SELECT FROM information_schema.tables\n        WHERE table_schema = 'web'\n        AND table_name = 'PushSubscription'\n      );\n    "], ["\n      SELECT EXISTS (\n        SELECT FROM information_schema.tables\n        WHERE table_schema = 'web'\n        AND table_name = 'PushSubscription'\n      );\n    "])))];
                case 11:
                    pushTableExists = _a.sent();
                    if (!!pushTableExists[0].exists) return [3 /*break*/, 15];
                    console.log('➕ Creating PushSubscription table...');
                    return [4 /*yield*/, prisma.$executeRaw(templateObject_9 || (templateObject_9 = __makeTemplateObject(["\n        CREATE TABLE \"web\".\"PushSubscription\" (\n          \"id\" TEXT NOT NULL,\n          \"userId\" TEXT NOT NULL,\n          \"endpoint\" TEXT NOT NULL,\n          \"p256dh\" TEXT,\n          \"auth\" TEXT,\n          \"createdAt\" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,\n          \"updatedAt\" TIMESTAMP(3) NOT NULL,\n\n          CONSTRAINT \"PushSubscription_pkey\" PRIMARY KEY (\"id\")\n        );\n      "], ["\n        CREATE TABLE \"web\".\"PushSubscription\" (\n          \"id\" TEXT NOT NULL,\n          \"userId\" TEXT NOT NULL,\n          \"endpoint\" TEXT NOT NULL,\n          \"p256dh\" TEXT,\n          \"auth\" TEXT,\n          \"createdAt\" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,\n          \"updatedAt\" TIMESTAMP(3) NOT NULL,\n\n          CONSTRAINT \"PushSubscription_pkey\" PRIMARY KEY (\"id\")\n        );\n      "])))];
                case 12:
                    _a.sent();
                    // Add unique constraint
                    return [4 /*yield*/, prisma.$executeRaw(templateObject_10 || (templateObject_10 = __makeTemplateObject(["\n        CREATE UNIQUE INDEX \"PushSubscription_userId_endpoint_key\" ON \"web\".\"PushSubscription\"(\"userId\", \"endpoint\");\n      "], ["\n        CREATE UNIQUE INDEX \"PushSubscription_userId_endpoint_key\" ON \"web\".\"PushSubscription\"(\"userId\", \"endpoint\");\n      "])))];
                case 13:
                    // Add unique constraint
                    _a.sent();
                    // Add foreign key constraint
                    return [4 /*yield*/, prisma.$executeRaw(templateObject_11 || (templateObject_11 = __makeTemplateObject(["\n        ALTER TABLE \"web\".\"PushSubscription\" ADD CONSTRAINT \"PushSubscription_userId_fkey\" FOREIGN KEY (\"userId\") REFERENCES \"web\".\"User\"(\"id\") ON DELETE CASCADE ON UPDATE CASCADE;\n      "], ["\n        ALTER TABLE \"web\".\"PushSubscription\" ADD CONSTRAINT \"PushSubscription_userId_fkey\" FOREIGN KEY (\"userId\") REFERENCES \"web\".\"User\"(\"id\") ON DELETE CASCADE ON UPDATE CASCADE;\n      "])))];
                case 14:
                    // Add foreign key constraint
                    _a.sent();
                    console.log('✅ PushSubscription table created successfully');
                    return [3 /*break*/, 16];
                case 15:
                    console.log('✅ PushSubscription table already exists');
                    _a.label = 16;
                case 16: return [3 /*break*/, 20];
                case 17:
                    error_1 = _a.sent();
                    console.error('❌ Error during migration:', error_1);
                    throw error_1;
                case 18: return [4 /*yield*/, prisma.$disconnect()];
                case 19:
                    _a.sent();
                    return [7 /*endfinally*/];
                case 20: return [2 /*return*/];
            }
        });
    });
}
// Run the migration
addPushSubscriptionsTable()
    .then(function () {
    console.log('🎉 Migration completed successfully');
    process.exit(0);
})
    .catch(function (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
});
var templateObject_1, templateObject_2, templateObject_3, templateObject_4, templateObject_5, templateObject_6, templateObject_7, templateObject_8, templateObject_9, templateObject_10, templateObject_11;
