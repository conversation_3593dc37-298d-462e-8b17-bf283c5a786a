import "./chunk-AG62LWSR.js";
import "./chunk-AGPRHSYH.js";
import "./chunk-RIXFT5AQ.js";
import {
  STYLE,
  add_locations,
  append_styles,
  bind_this,
  check_target,
  cleanup_styles,
  clsx,
  component,
  each,
  get as get2,
  hmr,
  if_block,
  index,
  init,
  legacy_api,
  legacy_rest_props,
  onDestroy,
  onMount,
  prop,
  set_attribute,
  set_attributes,
  set_class,
  set_style,
  setup_stores,
  slot,
  spread_props,
  store_get,
  store_unsub,
  validate_each_keys,
  validate_store,
  writable
} from "./chunk-NDLSR7SS.js";
import "./chunk-U7P2NEEE.js";
import {
  append,
  comment,
  ns_template,
  set_text,
  template,
  text
} from "./chunk-4ZPQDFFK.js";
import {
  FILENAME,
  HMR,
  apply,
  child,
  deep_read_state,
  derived_safe_equal,
  event,
  first_child,
  get,
  legacy_pre_effect,
  legacy_pre_effect_reset,
  mutable_source,
  pop,
  push,
  reset,
  set,
  sibling,
  strict_equals,
  template_effect,
  tick
} from "./chunk-QYXENPIM.js";
import "./chunk-VIZMNZTH.js";
import "./chunk-HNWPC2PS.js";
import "./chunk-C5KNTEDU.js";
import "./chunk-RVAV4ZRS.js";
import "./chunk-KWPVD4H7.js";

// node_modules/svelte-sonner/dist/Icon.svelte
Icon[FILENAME] = "node_modules/svelte-sonner/dist/Icon.svelte";
var root_1 = add_locations(ns_template(`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" height="20" width="20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd"></path></svg>`), Icon[FILENAME], [[5, 1, [[12, 2]]]]);
var root_3 = add_locations(ns_template(`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" height="20" width="20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path></svg>`), Icon[FILENAME], [[19, 1, [[26, 2]]]]);
var root_5 = add_locations(ns_template(`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" height="20" width="20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clip-rule="evenodd"></path></svg>`), Icon[FILENAME], [[33, 1, [[40, 2]]]]);
var root_7 = add_locations(ns_template(`<svg viewBox="0 0 64 64" fill="currentColor" height="20" width="20" xmlns="http://www.w3.org/2000/svg"><path d="M32.427,7.987c2.183,0.124 4,1.165 5.096,3.281l17.936,36.208c1.739,3.66 -0.954,8.585 -5.373,8.656l-36.119,0c-4.022,-0.064 -7.322,-4.631 -5.352,-8.696l18.271,-36.207c0.342,-0.65 0.498,-0.838 0.793,-1.179c1.186,-1.375 2.483,-2.111 4.748,-2.063Zm-0.295,3.997c-0.687,0.034 -1.316,0.419 -1.659,1.017c-6.312,11.979 -12.397,24.081 -18.301,36.267c-0.546,1.225 0.391,2.797 1.762,2.863c12.06,0.195 24.125,0.195 36.185,0c1.325,-0.064 2.321,-1.584 1.769,-2.85c-5.793,-12.184 -11.765,-24.286 -17.966,-36.267c-0.366,-0.651 -0.903,-1.042 -1.79,-1.03Z"></path><path d="M33.631,40.581l-3.348,0l-0.368,-16.449l4.1,0l-0.384,16.449Zm-3.828,5.03c0,-0.609 0.197,-1.113 0.592,-1.514c0.396,-0.4 0.935,-0.601 1.618,-0.601c0.684,0 1.223,0.201 1.618,0.601c0.395,0.401 0.593,0.905 0.593,1.514c0,0.587 -0.193,1.078 -0.577,1.473c-0.385,0.395 -0.929,0.593 -1.634,0.593c-0.705,0 -1.249,-0.198 -1.634,-0.593c-0.384,-0.395 -0.576,-0.886 -0.576,-1.473Z"></path></svg>`), Icon[FILENAME], [[47, 1, [[54, 2], [57, 2]]]]);
function Icon($$anchor, $$props) {
  check_target(new.target);
  push($$props, false, Icon);
  let type = prop($$props, "type", 8, "success");
  var fragment = comment();
  var node = first_child(fragment);
  {
    var consequent = ($$anchor2) => {
      var svg = root_1();
      append($$anchor2, svg);
    };
    var alternate = ($$anchor2, $$elseif) => {
      {
        var consequent_1 = ($$anchor3) => {
          var svg_1 = root_3();
          append($$anchor3, svg_1);
        };
        var alternate_1 = ($$anchor3, $$elseif2) => {
          {
            var consequent_2 = ($$anchor4) => {
              var svg_2 = root_5();
              append($$anchor4, svg_2);
            };
            var alternate_2 = ($$anchor4, $$elseif3) => {
              {
                var consequent_3 = ($$anchor5) => {
                  var svg_3 = root_7();
                  append($$anchor5, svg_3);
                };
                var alternate_3 = ($$anchor5) => {
                };
                if_block(
                  $$anchor4,
                  ($$render) => {
                    if (strict_equals(type(), "warning")) $$render(consequent_3);
                    else $$render(alternate_3, false);
                  },
                  $$elseif3
                );
              }
            };
            if_block(
              $$anchor3,
              ($$render) => {
                if (strict_equals(type(), "info")) $$render(consequent_2);
                else $$render(alternate_2, false);
              },
              $$elseif2
            );
          }
        };
        if_block(
          $$anchor2,
          ($$render) => {
            if (strict_equals(type(), "error")) $$render(consequent_1);
            else $$render(alternate_1, false);
          },
          $$elseif
        );
      }
    };
    if_block(node, ($$render) => {
      if (strict_equals(type(), "success")) $$render(consequent);
      else $$render(alternate, false);
    });
  }
  append($$anchor, fragment);
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  Icon = hmr(Icon, () => Icon[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Icon[HMR].source;
    set(Icon[HMR].source, module.default[HMR].original);
  });
}
var Icon_default = Icon;

// node_modules/svelte-sonner/dist/Loader.svelte
Loader[FILENAME] = "node_modules/svelte-sonner/dist/Loader.svelte";
var root_12 = add_locations(template(`<div class="sonner-loading-bar"></div>`), Loader[FILENAME], [[8, 3]]);
var root = add_locations(template(`<div class="sonner-loading-wrapper"><div class="sonner-spinner"></div></div>`), Loader[FILENAME], [[5, 0, [[6, 1]]]]);
function Loader($$anchor, $$props) {
  check_target(new.target);
  push($$props, false, Loader);
  let visible = prop($$props, "visible", 8);
  const bars = Array(12).fill(0);
  init();
  var div = root();
  var div_1 = child(div);
  each(div_1, 5, () => bars, index, ($$anchor2, _) => {
    var div_2 = root_12();
    append($$anchor2, div_2);
  });
  reset(div_1);
  reset(div);
  template_effect(() => set_attribute(div, "data-visible", visible()));
  append($$anchor, div);
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  Loader = hmr(Loader, () => Loader[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Loader[HMR].source;
    set(Loader[HMR].source, module.default[HMR].original);
  });
}
var Loader_default = Loader;

// node_modules/svelte-sonner/dist/internal/helpers.js
function cn(...classes) {
  return classes.filter(Boolean).join(" ");
}
var isBrowser = typeof document !== "undefined";
function clientWritable(initialValue) {
  const store = writable(initialValue);
  function set2(value) {
    if (isBrowser) {
      store.set(value);
    }
  }
  function update(updater) {
    if (isBrowser) {
      store.update(updater);
    }
  }
  return {
    subscribe: store.subscribe,
    set: set2,
    update
  };
}

// node_modules/svelte-sonner/dist/state.js
var toastsCounter = 0;
function createToastState() {
  const toasts = clientWritable([]);
  const heights = clientWritable([]);
  function addToast(data) {
    toasts.update((prev) => [data, ...prev]);
  }
  function create(data) {
    var _a;
    const { message: message2, ...rest } = data;
    const id = typeof (data == null ? void 0 : data.id) === "number" || data.id && ((_a = data.id) == null ? void 0 : _a.length) > 0 ? data.id : toastsCounter++;
    const dismissable = data.dismissable === void 0 ? true : data.dismissable;
    const type = data.type === void 0 ? "default" : data.type;
    const $toasts = get2(toasts);
    const alreadyExists = $toasts.find((toast2) => {
      return toast2.id === id;
    });
    if (alreadyExists) {
      toasts.update((prev) => prev.map((toast2) => {
        if (toast2.id === id) {
          return {
            ...toast2,
            ...data,
            id,
            title: message2,
            dismissable,
            type,
            updated: true
          };
        }
        return {
          ...toast2,
          updated: false
        };
      }));
    } else {
      addToast({ ...rest, id, title: message2, dismissable, type });
    }
    return id;
  }
  function dismiss(id) {
    if (id === void 0) {
      toasts.update((prev) => prev.map((toast2) => ({ ...toast2, dismiss: true })));
      return;
    }
    toasts.update((prev) => prev.map((toast2) => toast2.id === id ? { ...toast2, dismiss: true } : toast2));
    return id;
  }
  function remove(id) {
    if (id === void 0) {
      toasts.set([]);
      return;
    }
    toasts.update((prev) => prev.filter((toast2) => toast2.id !== id));
    return id;
  }
  function message(message2, data) {
    return create({ ...data, type: "default", message: message2 });
  }
  function error(message2, data) {
    return create({ ...data, type: "error", message: message2 });
  }
  function success(message2, data) {
    return create({ ...data, type: "success", message: message2 });
  }
  function info(message2, data) {
    return create({ ...data, type: "info", message: message2 });
  }
  function warning(message2, data) {
    return create({ ...data, type: "warning", message: message2 });
  }
  function loading(message2, data) {
    return create({ ...data, type: "loading", message: message2 });
  }
  function promise(promise2, data) {
    if (!data) {
      return;
    }
    let id = void 0;
    if (data.loading !== void 0) {
      id = create({
        ...data,
        promise: promise2,
        type: "loading",
        message: data.loading
      });
    }
    const p = promise2 instanceof Promise ? promise2 : promise2();
    let shouldDismiss = id !== void 0;
    p.then((response) => {
      if (response && typeof response.ok === "boolean" && !response.ok) {
        shouldDismiss = false;
        const message2 = typeof data.error === "function" ? (
          // @ts-expect-error: Incorrect response type
          data.error(`HTTP error! status: ${response.status}`)
        ) : data.error;
        create({ id, type: "error", message: message2 });
      } else if (data.success !== void 0) {
        shouldDismiss = false;
        const message2 = (
          // @ts-expect-error: TODO: Better function checking
          typeof data.success === "function" ? data.success(response) : data.success
        );
        create({ id, type: "success", message: message2 });
      }
    }).catch((error2) => {
      if (data.error !== void 0) {
        shouldDismiss = false;
        const message2 = (
          // @ts-expect-error: TODO: Better function checking
          typeof data.error === "function" ? data.error(error2) : data.error
        );
        create({ id, type: "error", message: message2 });
      }
    }).finally(() => {
      var _a;
      if (shouldDismiss) {
        dismiss(id);
        id = void 0;
      }
      (_a = data.finally) == null ? void 0 : _a.call(data);
    });
    return id;
  }
  function custom(component2, data) {
    const id = (data == null ? void 0 : data.id) || toastsCounter++;
    create({ component: component2, id, ...data });
    return id;
  }
  function removeHeight(id) {
    heights.update((prev) => prev.filter((height) => height.toastId !== id));
  }
  function setHeight(data) {
    const exists = get2(heights).find((el) => el.toastId === data.toastId);
    if (exists === void 0) {
      heights.update((prev) => [data, ...prev]);
      return;
    }
    heights.update((prev) => prev.map((el) => {
      if (el.toastId === data.toastId) {
        return data;
      } else {
        return el;
      }
    }));
  }
  function reset2() {
    toasts.set([]);
    heights.set([]);
  }
  return {
    // methods
    create,
    addToast,
    dismiss,
    remove,
    message,
    error,
    success,
    info,
    warning,
    loading,
    promise,
    custom,
    removeHeight,
    setHeight,
    reset: reset2,
    // stores
    toasts,
    heights
  };
}
var toastState = createToastState();
function toastFunction(message, data) {
  return toastState.create({
    message,
    ...data
  });
}
var basicToast = toastFunction;
var toast = Object.assign(basicToast, {
  success: toastState.success,
  info: toastState.info,
  warning: toastState.warning,
  error: toastState.error,
  custom: toastState.custom,
  message: toastState.message,
  promise: toastState.promise,
  dismiss: toastState.dismiss,
  loading: toastState.loading
});
var useEffect = (subscribe) => ({ subscribe });

// node_modules/svelte-sonner/dist/Toast.svelte
Toast[FILENAME] = "node_modules/svelte-sonner/dist/Toast.svelte";
var root_13 = add_locations(template(`<button aria-label="Close toast" data-close-button=""><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg></button>`), Toast[FILENAME], [
  [
    275,
    2,
    [[287, 3, [[298, 4], [299, 4]]]]
  ]
]);
var root_4 = add_locations(template(`<div data-icon=""><!> <!></div>`), Toast[FILENAME], [[312, 3]]);
var root_15 = add_locations(template(`<div data-title=""><!></div>`), Toast[FILENAME], [[331, 4]]);
var root_18 = add_locations(template(`<div data-description=""><!></div>`), Toast[FILENAME], [[346, 4]]);
var root_21 = add_locations(template(`<button data-button="" data-cancel=""> </button>`), Toast[FILENAME], [[367, 3]]);
var root_22 = add_locations(template(`<button data-button=""> </button>`), Toast[FILENAME], [[383, 3]]);
var root_32 = add_locations(template(`<!> <div data-content=""><!> <!></div> <!> <!>`, 1), Toast[FILENAME], [[329, 2]]);
var root2 = add_locations(template(`<li aria-atomic="true" role="status" data-sonner-toast=""><!> <!></li>`), Toast[FILENAME], [[235, 0]]);
function Toast($$anchor, $$props) {
  check_target(new.target);
  const $$sanitized_props = legacy_rest_props($$props, [
    "children",
    "$$slots",
    "$$events",
    "$$legacy"
  ]);
  push($$props, false, Toast);
  const [$$stores, $$cleanup] = setup_stores();
  const $heights = () => (validate_store(heights, "heights"), store_get(heights, "$heights", $$stores));
  const $effect = () => (validate_store(get(effect), "effect"), store_get(get(effect), "$effect", $$stores));
  const $toasts = () => (validate_store(toasts, "toasts"), store_get(toasts, "$toasts", $$stores));
  const isFront = mutable_source(void 0, true);
  const isVisible = mutable_source(void 0, true);
  const toastTitle = mutable_source(void 0, true);
  const toastDescription = mutable_source(void 0, true);
  const toastType = mutable_source(void 0, true);
  const toastClass = mutable_source(void 0, true);
  const toastDescriptionClass = mutable_source(void 0, true);
  const heightIndex = mutable_source(void 0, true);
  const coords = mutable_source(void 0, true);
  const toastsHeightBefore = mutable_source(void 0, true);
  const disabled = mutable_source(void 0, true);
  const isPromiseLoadingOrInfiniteDuration = mutable_source(void 0, true);
  const TOAST_LIFETIME = 4e3;
  const GAP = 14;
  const SWIPE_TRESHOLD = 20;
  const TIME_BEFORE_UNMOUNT = 200;
  const SCALE_MULTIPLIER = 0.05;
  const defaultClasses = {
    toast: "",
    title: "",
    description: "",
    loader: "",
    closeButton: "",
    cancelButton: "",
    actionButton: "",
    action: "",
    warning: "",
    error: "",
    success: "",
    default: "",
    info: "",
    loading: ""
  };
  const {
    toasts,
    heights,
    removeHeight,
    setHeight,
    remove
  } = toastState;
  let toast2 = prop($$props, "toast", 9);
  let index2 = prop($$props, "index", 9);
  let expanded = prop($$props, "expanded", 9);
  let invert = prop($$props, "invert", 13);
  let position = prop($$props, "position", 9);
  let visibleToasts = prop($$props, "visibleToasts", 9);
  let expandByDefault = prop($$props, "expandByDefault", 9);
  let closeButton = prop($$props, "closeButton", 9);
  let interacting = prop($$props, "interacting", 9);
  let cancelButtonStyle = prop($$props, "cancelButtonStyle", 9, "");
  let actionButtonStyle = prop($$props, "actionButtonStyle", 9, "");
  let duration = prop($$props, "duration", 9, 4e3);
  let descriptionClass = prop($$props, "descriptionClass", 9, "");
  let classes = prop($$props, "classes", 29, () => ({}));
  let unstyled = prop($$props, "unstyled", 9, false);
  let mounted = mutable_source(false, true);
  let removed = mutable_source(false, true);
  let swiping = mutable_source(false, true);
  let swipeOut = mutable_source(false, true);
  let offsetBeforeRemove = mutable_source(0, true);
  let initialHeight = mutable_source(0, true);
  let toastRef = mutable_source(void 0, true);
  let offset = mutable_source(0, true);
  let closeTimerStartTimeRef = 0;
  let lastCloseTimerStartTimeRef = 0;
  let pointerStartRef = null;
  async function updateHeights() {
    if (!get(mounted)) {
      return;
    }
    await tick();
    let scale;
    if (expanded() || expandByDefault()) {
      scale = 1;
    } else {
      scale = 1 - index2() * SCALE_MULTIPLIER;
    }
    get(toastRef).style.setProperty("height", "auto");
    const offsetHeight = get(toastRef).offsetHeight;
    const rectHeight = get(toastRef).getBoundingClientRect().height;
    const scaledRectHeight = Math.round((rectHeight / scale + Number.EPSILON) * 100) / 100;
    get(toastRef).style.removeProperty("height");
    let finalHeight;
    if (Math.abs(scaledRectHeight - offsetHeight) < 1) {
      finalHeight = scaledRectHeight;
    } else {
      finalHeight = offsetHeight;
    }
    set(initialHeight, finalHeight);
    setHeight({ toastId: toast2().id, height: finalHeight });
  }
  function deleteToast() {
    set(removed, true);
    set(offsetBeforeRemove, get(offset));
    removeHeight(toast2().id);
    setTimeout(
      () => {
        remove(toast2().id);
      },
      TIME_BEFORE_UNMOUNT
    );
  }
  let timeoutId = mutable_source(void 0, true);
  let remainingTime = mutable_source(toast2().duration || duration() || TOAST_LIFETIME, true);
  function pauseTimer() {
    if (lastCloseTimerStartTimeRef < closeTimerStartTimeRef) {
      const elapsedTime = (/* @__PURE__ */ new Date()).getTime() - closeTimerStartTimeRef;
      set(remainingTime, get(remainingTime) - elapsedTime);
    }
    lastCloseTimerStartTimeRef = (/* @__PURE__ */ new Date()).getTime();
  }
  function startTimer() {
    closeTimerStartTimeRef = (/* @__PURE__ */ new Date()).getTime();
    set(timeoutId, setTimeout(
      () => {
        var _a, _b;
        (_b = (_a = toast2()).onAutoClose) == null ? void 0 : _b.call(_a, toast2());
        deleteToast();
      },
      get(remainingTime)
    ));
  }
  let effect = mutable_source(void 0, true);
  onMount(() => {
    set(mounted, true);
    const height = get(toastRef).getBoundingClientRect().height;
    set(initialHeight, height);
    setHeight({ toastId: toast2().id, height });
    return () => removeHeight(toast2().id);
  });
  function onPointerDown(event2) {
    if (get(disabled)) {
      return;
    }
    set(offsetBeforeRemove, get(offset));
    const target = event2.target;
    target.setPointerCapture(event2.pointerId);
    if (strict_equals(target.tagName, "BUTTON")) {
      return;
    }
    set(swiping, true);
    pointerStartRef = { x: event2.clientX, y: event2.clientY };
  }
  function onPointerUp() {
    var _a, _b, _c;
    if (get(swipeOut)) {
      return;
    }
    pointerStartRef = null;
    const swipeAmount = Number(((_a = get(toastRef)) == null ? void 0 : _a.style.getPropertyValue("--swipe-amount").replace("px", "")) || 0);
    if (Math.abs(swipeAmount) >= SWIPE_TRESHOLD) {
      set(offsetBeforeRemove, get(offset));
      (_c = (_b = toast2()).onDismiss) == null ? void 0 : _c.call(_b, toast2());
      deleteToast();
      set(swipeOut, true);
      return;
    }
    get(toastRef).style.setProperty("--swipe-amount", "0px");
    set(swiping, false);
  }
  function onPointerMove(event2) {
    if (!pointerStartRef) {
      return;
    }
    const yPosition = event2.clientY - pointerStartRef.y;
    const xPosition = event2.clientX - pointerStartRef.x;
    const clamp = strict_equals(get(coords)[0], "top") ? Math.min : Math.max;
    const clampedY = clamp(0, yPosition);
    const swipeStartThreshold = strict_equals(event2.pointerType, "touch") ? 10 : 2;
    const isAllowedToSwipe = Math.abs(clampedY) > swipeStartThreshold;
    if (isAllowedToSwipe) {
      get(toastRef).style.setProperty("--swipe-amount", `${yPosition}px`);
    } else if (Math.abs(xPosition) > swipeStartThreshold) {
      pointerStartRef = null;
    }
  }
  legacy_pre_effect(() => deep_read_state(classes()), () => {
    classes({ ...defaultClasses, ...classes() });
  });
  legacy_pre_effect(() => deep_read_state(index2()), () => {
    set(isFront, strict_equals(index2(), 0));
  });
  legacy_pre_effect(
    () => (deep_read_state(index2()), deep_read_state(visibleToasts())),
    () => {
      set(isVisible, index2() + 1 <= visibleToasts());
    }
  );
  legacy_pre_effect(() => deep_read_state(toast2()), () => {
    set(toastTitle, toast2().title);
  });
  legacy_pre_effect(() => deep_read_state(toast2()), () => {
    set(toastDescription, toast2().description);
  });
  legacy_pre_effect(() => deep_read_state(toast2()), () => {
    set(toastType, toast2().type);
  });
  legacy_pre_effect(() => deep_read_state(toast2()), () => {
    set(toastClass, toast2().class || "");
  });
  legacy_pre_effect(() => deep_read_state(toast2()), () => {
    set(toastDescriptionClass, toast2().descriptionClass || "");
  });
  legacy_pre_effect(
    () => ($heights(), deep_read_state(toast2())),
    () => {
      set(heightIndex, $heights().findIndex((height) => strict_equals(height.toastId, toast2().id)) || 0);
    }
  );
  legacy_pre_effect(() => deep_read_state(position()), () => {
    set(coords, position().split("-"));
  });
  legacy_pre_effect(() => ($heights(), get(heightIndex)), () => {
    set(toastsHeightBefore, $heights().reduce(
      (prev, curr, reducerIndex) => {
        if (reducerIndex >= get(heightIndex)) return prev;
        return prev + curr.height;
      },
      0
    ));
  });
  legacy_pre_effect(
    () => (deep_read_state(invert()), deep_read_state(toast2())),
    () => {
      invert(toast2().invert || invert());
    }
  );
  legacy_pre_effect(() => get(toastType), () => {
    set(disabled, strict_equals(get(toastType), "loading"));
  });
  legacy_pre_effect(
    () => (get(heightIndex), get(toastsHeightBefore)),
    () => {
      set(offset, Math.round(get(heightIndex) * GAP + get(toastsHeightBefore)));
    }
  );
  legacy_pre_effect(
    () => (get(toastTitle), get(toastDescription)),
    () => {
      get(toastTitle), get(toastDescription), updateHeights();
    }
  );
  legacy_pre_effect(
    () => (deep_read_state(toast2()), get(timeoutId), deep_read_state(duration())),
    () => {
      if (toast2().updated) {
        clearTimeout(get(timeoutId));
        set(remainingTime, toast2().duration || duration() || TOAST_LIFETIME);
        startTimer();
      }
    }
  );
  legacy_pre_effect(
    () => (deep_read_state(toast2()), get(toastType)),
    () => {
      set(isPromiseLoadingOrInfiniteDuration, toast2().promise && strict_equals(get(toastType), "loading") || strict_equals(toast2().duration, Number.POSITIVE_INFINITY));
    }
  );
  legacy_pre_effect(
    () => (useEffect, get(isPromiseLoadingOrInfiniteDuration), deep_read_state(expanded()), deep_read_state(interacting()), get(timeoutId)),
    () => {
      store_unsub(
        set(effect, useEffect(() => {
          if (!get(isPromiseLoadingOrInfiniteDuration)) {
            if (expanded() || interacting()) {
              pauseTimer();
            } else {
              startTimer();
            }
          }
          return () => clearTimeout(get(timeoutId));
        })),
        "$effect",
        $$stores
      );
    }
  );
  legacy_pre_effect(() => $effect(), () => {
    $effect();
  });
  legacy_pre_effect(() => deep_read_state(toast2()), () => {
    if (toast2().delete) {
      deleteToast();
    }
  });
  legacy_pre_effect_reset();
  init(true);
  var li = root2();
  set_attribute(li, "tabindex", 0);
  let styles;
  var node = child(li);
  {
    var consequent = ($$anchor2) => {
      var button = root_13();
      template_effect(
        ($0) => {
          set_attribute(button, "data-disabled", get(disabled));
          set_class(button, 1, $0);
        },
        [
          () => {
            var _a, _b, _c;
            return clsx(cn((_a = classes()) == null ? void 0 : _a.closeButton, (_c = (_b = toast2()) == null ? void 0 : _b.classes) == null ? void 0 : _c.closeButton));
          }
        ],
        derived_safe_equal
      );
      event("click", button, function(...$$args) {
        apply(() => get(disabled) ? void 0 : () => {
          var _a, _b;
          deleteToast();
          (_b = (_a = toast2()).onDismiss) == null ? void 0 : _b.call(_a, toast2());
        }, this, $$args, Toast, [279, 13]);
      });
      append($$anchor2, button);
    };
    if_block(node, ($$render) => {
      if (closeButton() && !toast2().component) $$render(consequent);
    });
  }
  var node_1 = sibling(node, 2);
  {
    var consequent_1 = ($$anchor2) => {
      var fragment = comment();
      var node_2 = first_child(fragment);
      component(node_2, () => toast2().component, ($$anchor3, $$component) => {
        $$component($$anchor3, spread_props(() => toast2().componentProps, { $$events: { closeToast: deleteToast } }));
      });
      append($$anchor2, fragment);
    };
    var alternate = ($$anchor2) => {
      var fragment_1 = root_32();
      var node_3 = first_child(fragment_1);
      {
        var consequent_8 = ($$anchor3) => {
          var div = root_4();
          var node_4 = child(div);
          {
            var consequent_2 = ($$anchor4) => {
              var fragment_2 = comment();
              var node_5 = first_child(fragment_2);
              slot(node_5, $$props, "loading-icon", {}, null);
              append($$anchor4, fragment_2);
            };
            if_block(node_4, ($$render) => {
              if ((toast2().promise || strict_equals(get(toastType), "loading")) && !toast2().icon) $$render(consequent_2);
            });
          }
          var node_6 = sibling(node_4, 2);
          {
            var consequent_3 = ($$anchor4) => {
              var fragment_3 = comment();
              var node_7 = first_child(fragment_3);
              component(node_7, () => toast2().icon, ($$anchor5, $$component) => {
                $$component($$anchor5, {});
              });
              append($$anchor4, fragment_3);
            };
            var alternate_1 = ($$anchor4, $$elseif) => {
              {
                var consequent_4 = ($$anchor5) => {
                  var fragment_4 = comment();
                  var node_8 = first_child(fragment_4);
                  slot(node_8, $$props, "success-icon", {}, null);
                  append($$anchor5, fragment_4);
                };
                var alternate_2 = ($$anchor5, $$elseif2) => {
                  {
                    var consequent_5 = ($$anchor6) => {
                      var fragment_5 = comment();
                      var node_9 = first_child(fragment_5);
                      slot(node_9, $$props, "error-icon", {}, null);
                      append($$anchor6, fragment_5);
                    };
                    var alternate_3 = ($$anchor6, $$elseif3) => {
                      {
                        var consequent_6 = ($$anchor7) => {
                          var fragment_6 = comment();
                          var node_10 = first_child(fragment_6);
                          slot(node_10, $$props, "warning-icon", {}, null);
                          append($$anchor7, fragment_6);
                        };
                        var alternate_4 = ($$anchor7, $$elseif4) => {
                          {
                            var consequent_7 = ($$anchor8) => {
                              var fragment_7 = comment();
                              var node_11 = first_child(fragment_7);
                              slot(node_11, $$props, "info-icon", {}, null);
                              append($$anchor8, fragment_7);
                            };
                            if_block(
                              $$anchor7,
                              ($$render) => {
                                if (strict_equals(get(toastType), "info")) $$render(consequent_7);
                              },
                              $$elseif4
                            );
                          }
                        };
                        if_block(
                          $$anchor6,
                          ($$render) => {
                            if (strict_equals(get(toastType), "warning")) $$render(consequent_6);
                            else $$render(alternate_4, false);
                          },
                          $$elseif3
                        );
                      }
                    };
                    if_block(
                      $$anchor5,
                      ($$render) => {
                        if (strict_equals(get(toastType), "error")) $$render(consequent_5);
                        else $$render(alternate_3, false);
                      },
                      $$elseif2
                    );
                  }
                };
                if_block(
                  $$anchor4,
                  ($$render) => {
                    if (strict_equals(get(toastType), "success")) $$render(consequent_4);
                    else $$render(alternate_2, false);
                  },
                  $$elseif
                );
              }
            };
            if_block(node_6, ($$render) => {
              if (toast2().icon) $$render(consequent_3);
              else $$render(alternate_1, false);
            });
          }
          reset(div);
          append($$anchor3, div);
        };
        if_block(node_3, ($$render) => {
          if (strict_equals(get(toastType), "default", false) || toast2().icon || toast2().promise) $$render(consequent_8);
        });
      }
      var div_1 = sibling(node_3, 2);
      var node_12 = child(div_1);
      {
        var consequent_10 = ($$anchor3) => {
          var div_2 = root_15();
          var node_13 = child(div_2);
          {
            var consequent_9 = ($$anchor4) => {
              var fragment_8 = comment();
              var node_14 = first_child(fragment_8);
              component(node_14, () => toast2().title, ($$anchor5, $$component) => {
                $$component($$anchor5, spread_props(() => toast2().componentProps));
              });
              append($$anchor4, fragment_8);
            };
            var alternate_5 = ($$anchor4) => {
              var text2 = text();
              template_effect(() => set_text(text2, toast2().title));
              append($$anchor4, text2);
            };
            if_block(node_13, ($$render) => {
              if (strict_equals(typeof toast2().title, "string", false)) $$render(consequent_9);
              else $$render(alternate_5, false);
            });
          }
          reset(div_2);
          template_effect(
            ($0) => set_class(div_2, 1, $0),
            [
              () => {
                var _a, _b, _c;
                return clsx(cn((_a = classes()) == null ? void 0 : _a.title, (_c = (_b = toast2()) == null ? void 0 : _b.classes) == null ? void 0 : _c.title));
              }
            ],
            derived_safe_equal
          );
          append($$anchor3, div_2);
        };
        if_block(node_12, ($$render) => {
          if (toast2().title) $$render(consequent_10);
        });
      }
      var node_15 = sibling(node_12, 2);
      {
        var consequent_12 = ($$anchor3) => {
          var div_3 = root_18();
          var node_16 = child(div_3);
          {
            var consequent_11 = ($$anchor4) => {
              var fragment_10 = comment();
              var node_17 = first_child(fragment_10);
              component(node_17, () => toast2().description, ($$anchor5, $$component) => {
                $$component($$anchor5, spread_props(() => toast2().componentProps));
              });
              append($$anchor4, fragment_10);
            };
            var alternate_6 = ($$anchor4) => {
              var text_1 = text();
              template_effect(() => set_text(text_1, toast2().description));
              append($$anchor4, text_1);
            };
            if_block(node_16, ($$render) => {
              if (strict_equals(typeof toast2().description, "string", false)) $$render(consequent_11);
              else $$render(alternate_6, false);
            });
          }
          reset(div_3);
          template_effect(
            ($0) => set_class(div_3, 1, $0),
            [
              () => {
                var _a, _b;
                return clsx(cn(descriptionClass(), get(toastDescriptionClass), (_a = classes()) == null ? void 0 : _a.description, (_b = toast2().classes) == null ? void 0 : _b.description));
              }
            ],
            derived_safe_equal
          );
          append($$anchor3, div_3);
        };
        if_block(node_15, ($$render) => {
          if (toast2().description) $$render(consequent_12);
        });
      }
      reset(div_1);
      var node_18 = sibling(div_1, 2);
      {
        var consequent_13 = ($$anchor3) => {
          var button_1 = root_21();
          var text_2 = child(button_1, true);
          reset(button_1);
          template_effect(
            ($0) => {
              set_style(button_1, cancelButtonStyle());
              set_class(button_1, 1, $0);
              set_text(text_2, toast2().cancel.label);
            },
            [
              () => {
                var _a, _b, _c;
                return clsx(cn((_a = classes()) == null ? void 0 : _a.cancelButton, (_c = (_b = toast2()) == null ? void 0 : _b.classes) == null ? void 0 : _c.cancelButton));
              }
            ],
            derived_safe_equal
          );
          event("click", button_1, () => {
            var _a;
            deleteToast();
            if ((_a = toast2().cancel) == null ? void 0 : _a.onClick) {
              toast2().cancel.onClick();
            }
          });
          append($$anchor3, button_1);
        };
        if_block(node_18, ($$render) => {
          if (toast2().cancel) $$render(consequent_13);
        });
      }
      var node_19 = sibling(node_18, 2);
      {
        var consequent_14 = ($$anchor3) => {
          var button_2 = root_22();
          var text_3 = child(button_2, true);
          reset(button_2);
          template_effect(
            ($0) => {
              set_style(button_2, actionButtonStyle());
              set_class(button_2, 1, $0);
              set_text(text_3, toast2().action.label);
            },
            [
              () => {
                var _a, _b, _c;
                return clsx(cn((_a = classes()) == null ? void 0 : _a.actionButton, (_c = (_b = toast2()) == null ? void 0 : _b.classes) == null ? void 0 : _c.actionButton));
              }
            ],
            derived_safe_equal
          );
          event("click", button_2, (event2) => {
            var _a;
            (_a = toast2().action) == null ? void 0 : _a.onClick(event2);
            if (event2.defaultPrevented) return;
            deleteToast();
          });
          append($$anchor3, button_2);
        };
        if_block(node_19, ($$render) => {
          if (toast2().action) $$render(consequent_14);
        });
      }
      append($$anchor2, fragment_1);
    };
    if_block(node_1, ($$render) => {
      if (toast2().component) $$render(consequent_1);
      else $$render(alternate, false);
    });
  }
  reset(li);
  bind_this(li, ($$value) => set(toastRef, $$value), () => get(toastRef));
  template_effect(
    ($0, $1, $2) => {
      var _a;
      set_attribute(li, "aria-live", toast2().important ? "assertive" : "polite");
      set_class(li, 1, $0);
      set_attribute(li, "data-styled", !(toast2().component || ((_a = toast2()) == null ? void 0 : _a.unstyled) || unstyled()));
      set_attribute(li, "data-mounted", get(mounted));
      set_attribute(li, "data-promise", $1);
      set_attribute(li, "data-removed", get(removed));
      set_attribute(li, "data-visible", get(isVisible));
      set_attribute(li, "data-y-position", get(coords)[0]);
      set_attribute(li, "data-x-position", get(coords)[1]);
      set_attribute(li, "data-index", index2());
      set_attribute(li, "data-front", get(isFront));
      set_attribute(li, "data-swiping", get(swiping));
      set_attribute(li, "data-type", get(toastType));
      set_attribute(li, "data-invert", invert());
      set_attribute(li, "data-swipe-out", get(swipeOut));
      set_attribute(li, "data-expanded", $2);
      styles = set_style(li, `${$$sanitized_props.style} ${toast2().style}`, styles, {
        "--index": index2(),
        "--toasts-before": index2(),
        "--z-index": $toasts().length - index2(),
        "--offset": `${get(removed) ? get(offsetBeforeRemove) : get(offset)}px`,
        "--initial-height": `${get(initialHeight)}px`
      });
    },
    [
      () => {
        var _a, _b, _c, _d, _e, _f;
        return clsx(cn($$sanitized_props.class, get(toastClass), (_a = classes()) == null ? void 0 : _a.toast, (_c = (_b = toast2()) == null ? void 0 : _b.classes) == null ? void 0 : _c.toast, (_d = classes()) == null ? void 0 : _d[get(toastType)], (_f = (_e = toast2()) == null ? void 0 : _e.classes) == null ? void 0 : _f[get(toastType)]));
      },
      () => Boolean(toast2().promise),
      () => Boolean(expanded() || expandByDefault() && get(mounted))
    ],
    derived_safe_equal
  );
  event("pointerdown", li, onPointerDown);
  event("pointerup", li, onPointerUp);
  event("pointermove", li, onPointerMove);
  append($$anchor, li);
  var $$pop = pop({ ...legacy_api() });
  $$cleanup();
  return $$pop;
}
if (import.meta.hot) {
  Toast = hmr(Toast, () => Toast[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Toast[HMR].source;
    set(Toast[HMR].source, module.default[HMR].original);
  });
}
var Toast_default = Toast;

// node_modules/svelte-sonner/dist/Toaster.svelte
Toaster[FILENAME] = "node_modules/svelte-sonner/dist/Toaster.svelte";
var root_2 = add_locations(template(`<ol></ol>`), Toaster[FILENAME], [[164, 3]]);
var root_14 = add_locations(template(`<section class="s-KjATBeAaNTDC"></section>`), Toaster[FILENAME], [[162, 1]]);
var $$css = {
  hash: "s-KjATBeAaNTDC",
  code: "\n	:where(html[dir='ltr']),\n	:where([data-sonner-toaster][dir='ltr']) {\n		--toast-icon-margin-start: -3px;\n		--toast-icon-margin-end: 4px;\n		--toast-svg-margin-start: -1px;\n		--toast-svg-margin-end: 0px;\n		--toast-button-margin-start: auto;\n		--toast-button-margin-end: 0;\n		--toast-close-button-start: 0;\n		--toast-close-button-end: unset;\n		--toast-close-button-transform: translate(-35%, -35%);\n	}\n\n	:where(html[dir='rtl']),\n	:where([data-sonner-toaster][dir='rtl']) {\n		--toast-icon-margin-start: 4px;\n		--toast-icon-margin-end: -3px;\n		--toast-svg-margin-start: 0px;\n		--toast-svg-margin-end: -1px;\n		--toast-button-margin-start: 0;\n		--toast-button-margin-end: auto;\n		--toast-close-button-start: unset;\n		--toast-close-button-end: 0;\n		--toast-close-button-transform: translate(35%, -35%);\n	}\n\n	:where([data-sonner-toaster]) {\n		position: fixed;\n		width: var(--width);\n		font-family:\n			ui-sans-serif,\n			system-ui,\n			-apple-system,\n			BlinkMacSystemFont,\n			Segoe UI,\n			Roboto,\n			Helvetica Neue,\n			Arial,\n			Noto Sans,\n			sans-serif,\n			Apple Color Emoji,\n			Segoe UI Emoji,\n			Segoe UI Symbol,\n			Noto Color Emoji;\n		--gray1: hsl(0, 0%, 99%);\n		--gray2: hsl(0, 0%, 97.3%);\n		--gray3: hsl(0, 0%, 95.1%);\n		--gray4: hsl(0, 0%, 93%);\n		--gray5: hsl(0, 0%, 90.9%);\n		--gray6: hsl(0, 0%, 88.7%);\n		--gray7: hsl(0, 0%, 85.8%);\n		--gray8: hsl(0, 0%, 78%);\n		--gray9: hsl(0, 0%, 56.1%);\n		--gray10: hsl(0, 0%, 52.3%);\n		--gray11: hsl(0, 0%, 43.5%);\n		--gray12: hsl(0, 0%, 9%);\n		--border-radius: 8px;\n		box-sizing: border-box;\n		padding: 0;\n		margin: 0;\n		list-style: none;\n		outline: none;\n		z-index: 999999999;\n	}\n\n	:where([data-sonner-toaster][data-x-position='right']) {\n		right: max(var(--offset), env(safe-area-inset-right));\n	}\n\n	:where([data-sonner-toaster][data-x-position='left']) {\n		left: max(var(--offset), env(safe-area-inset-left));\n	}\n\n	:where([data-sonner-toaster][data-x-position='center']) {\n		left: 50%;\n		transform: translateX(-50%);\n	}\n\n	:where([data-sonner-toaster][data-y-position='top']) {\n		top: max(var(--offset), env(safe-area-inset-top));\n	}\n\n	:where([data-sonner-toaster][data-y-position='bottom']) {\n		bottom: max(var(--offset), env(safe-area-inset-bottom));\n	}\n\n	:where([data-sonner-toast]) {\n		--y: translateY(100%);\n		--lift-amount: calc(var(--lift) * var(--gap));\n		z-index: var(--z-index);\n		position: absolute;\n		opacity: 0;\n		transform: var(--y);\n		filter: blur(0);\n		/* https://stackoverflow.com/questions/48124372/pointermove-event-not-working-with-touch-why-not */\n		touch-action: none;\n		transition:\n			transform 400ms,\n			opacity 400ms,\n			height 400ms,\n			box-shadow 200ms;\n		box-sizing: border-box;\n		outline: none;\n		overflow-wrap: anywhere;\n	}\n\n	:where([data-sonner-toast][data-styled='true']) {\n		padding: 16px;\n		background: var(--normal-bg);\n		border: 1px solid var(--normal-border);\n		color: var(--normal-text);\n		border-radius: var(--border-radius);\n		box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);\n		width: var(--width);\n		font-size: 13px;\n		display: flex;\n		align-items: center;\n		gap: 6px;\n	}\n\n	:where([data-sonner-toast]:focus-visible) {\n		box-shadow:\n			0px 4px 12px rgba(0, 0, 0, 0.1),\n			0 0 0 2px rgba(0, 0, 0, 0.2);\n	}\n\n	:where([data-sonner-toast][data-y-position='top']) {\n		top: 0;\n		--y: translateY(-100%);\n		--lift: 1;\n		--lift-amount: calc(1 * var(--gap));\n	}\n\n	:where([data-sonner-toast][data-y-position='bottom']) {\n		bottom: 0;\n		--y: translateY(100%);\n		--lift: -1;\n		--lift-amount: calc(var(--lift) * var(--gap));\n	}\n\n	:where([data-sonner-toast]) :where([data-description]) {\n		font-weight: 400;\n		line-height: 1.4;\n		color: inherit;\n	}\n\n	:where([data-sonner-toast]) :where([data-title]) {\n		font-weight: 500;\n		line-height: 1.5;\n		color: inherit;\n	}\n\n	:where([data-sonner-toast]) :where([data-icon]) {\n		display: flex;\n		height: 16px;\n		width: 16px;\n		position: relative;\n		justify-content: flex-start;\n		align-items: center;\n		flex-shrink: 0;\n		margin-left: var(--toast-icon-margin-start);\n		margin-right: var(--toast-icon-margin-end);\n	}\n\n	:where([data-sonner-toast][data-promise='true']) :where([data-icon]) > svg {\n		opacity: 0;\n		transform: scale(0.8);\n		transform-origin: center;\n		animation: sonner-fade-in 300ms ease forwards;\n	}\n\n	:where([data-sonner-toast]) :where([data-icon]) > * {\n		flex-shrink: 0;\n	}\n\n	:where([data-sonner-toast]) :where([data-icon]) svg {\n		margin-left: var(--toast-svg-margin-start);\n		margin-right: var(--toast-svg-margin-end);\n	}\n\n	:where([data-sonner-toast]) :where([data-content]) {\n		display: flex;\n		flex-direction: column;\n		gap: 2px;\n	}\n\n	[data-sonner-toast][data-styled='true'] [data-button] {\n		border-radius: 4px;\n		padding-left: 8px;\n		padding-right: 8px;\n		height: 24px;\n		font-size: 12px;\n		color: var(--normal-bg);\n		background: var(--normal-text);\n		margin-left: var(--toast-button-margin-start);\n		margin-right: var(--toast-button-margin-end);\n		border: none;\n		cursor: pointer;\n		outline: none;\n		display: flex;\n		align-items: center;\n		flex-shrink: 0;\n		transition:\n			opacity 400ms,\n			box-shadow 200ms;\n	}\n\n	:where([data-sonner-toast]) :where([data-button]):focus-visible {\n		box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.4);\n	}\n\n	:where([data-sonner-toast]) :where([data-button]):first-of-type {\n		margin-left: var(--toast-button-margin-start);\n		margin-right: var(--toast-button-margin-end);\n	}\n\n	:where([data-sonner-toast]) :where([data-cancel]) {\n		color: var(--normal-text);\n		background: rgba(0, 0, 0, 0.08);\n	}\n\n	:where([data-sonner-toast][data-theme='dark']) :where([data-cancel]) {\n		background: rgba(255, 255, 255, 0.3);\n	}\n\n	:where([data-sonner-toast]) :where([data-close-button]) {\n		position: absolute;\n		left: var(--toast-close-button-start);\n		right: var(--toast-close-button-end);\n		top: 0;\n		height: 20px;\n		width: 20px;\n		display: flex;\n		justify-content: center;\n		align-items: center;\n		padding: 0;\n		background: var(--gray1);\n		color: var(--gray12);\n		border: 1px solid var(--gray4);\n		transform: var(--toast-close-button-transform);\n		border-radius: 50%;\n		cursor: pointer;\n		z-index: 1;\n		transition:\n			opacity 100ms,\n			background 200ms,\n			border-color 200ms;\n	}\n\n	:where([data-sonner-toast]) :where([data-close-button]):focus-visible {\n		box-shadow:\n			0px 4px 12px rgba(0, 0, 0, 0.1),\n			0 0 0 2px rgba(0, 0, 0, 0.2);\n	}\n\n	:where([data-sonner-toast]) :where([data-disabled='true']) {\n		cursor: not-allowed;\n	}\n\n	:where([data-sonner-toast]):hover :where([data-close-button]):hover {\n		background: var(--gray2);\n		border-color: var(--gray5);\n	}\n\n	/* Leave a ghost div to avoid setting hover to false when swiping out */\n	:where([data-sonner-toast][data-swiping='true'])::before {\n		content: '';\n		position: absolute;\n		left: 0;\n		right: 0;\n		height: 100%;\n		z-index: -1;\n	}\n\n	:where(\n			[data-sonner-toast][data-y-position='top'][data-swiping='true']\n		)::before {\n		/* y 50% needed to distribute height additional height evenly */\n		bottom: 50%;\n		transform: scaleY(3) translateY(50%);\n	}\n\n	:where(\n			[data-sonner-toast][data-y-position='bottom'][data-swiping='true']\n		)::before {\n		/* y -50% needed to distribute height additional height evenly */\n		top: 50%;\n		transform: scaleY(3) translateY(-50%);\n	}\n\n	/* Leave a ghost div to avoid setting hover to false when transitioning out */\n	:where(\n			[data-sonner-toast][data-swiping='false'][data-removed='true']\n		)::before {\n		content: '';\n		position: absolute;\n		inset: 0;\n		transform: scaleY(2);\n	}\n\n	/* Needed to avoid setting hover to false when inbetween toasts */\n	:where([data-sonner-toast])::after {\n		content: '';\n		position: absolute;\n		left: 0;\n		height: calc(var(--gap) + 1px);\n		bottom: 100%;\n		width: 100%;\n	}\n\n	:where([data-sonner-toast][data-mounted='true']) {\n		--y: translateY(0);\n		opacity: 1;\n	}\n\n	:where([data-sonner-toast][data-expanded='false'][data-front='false']) {\n		--scale: var(--toasts-before) * 0.05 + 1;\n		--y: translateY(calc(var(--lift-amount) * var(--toasts-before)))\n			scale(calc(-1 * var(--scale)));\n		height: var(--front-toast-height);\n	}\n\n	:where([data-sonner-toast]) > * {\n		transition: opacity 400ms;\n	}\n\n	:where(\n			[data-sonner-toast][data-expanded='false'][data-front='false'][data-styled='true']\n		)\n		> * {\n		opacity: 0;\n	}\n\n	:where([data-sonner-toast][data-visible='false']) {\n		opacity: 0;\n		pointer-events: none;\n	}\n\n	:where([data-sonner-toast][data-mounted='true'][data-expanded='true']) {\n		--y: translateY(calc(var(--lift) * var(--offset)));\n		height: var(--initial-height);\n	}\n\n	:where(\n			[data-sonner-toast][data-removed='true'][data-front='true'][data-swipe-out='false']\n		) {\n		--y: translateY(calc(var(--lift) * -100%));\n		opacity: 0;\n	}\n\n	:where(\n			[data-sonner-toast][data-removed='true'][data-front='false'][data-swipe-out='false'][data-expanded='true']\n		) {\n		--y: translateY(\n			calc(var(--lift) * var(--offset) + var(--lift) * -100%)\n		);\n		opacity: 0;\n	}\n\n	:where(\n			[data-sonner-toast][data-removed='true'][data-front='false'][data-swipe-out='false'][data-expanded='false']\n		) {\n		--y: translateY(40%);\n		opacity: 0;\n		transition:\n			transform 500ms,\n			opacity 200ms;\n	}\n\n	/* Bump up the height to make sure hover state doesn't get set to false */\n	:where(\n			[data-sonner-toast][data-removed='true'][data-front='false']\n		)::before {\n		height: calc(var(--initial-height) + 20%);\n	}\n\n	[data-sonner-toast][data-swiping='true'] {\n		transform: var(--y) translateY(var(--swipe-amount, 0px));\n		transition: none;\n	}\n\n	[data-sonner-toast][data-swipe-out='true'][data-y-position='bottom'],\n	[data-sonner-toast][data-swipe-out='true'][data-y-position='top'] {\n		animation: swipe-out 200ms ease-out forwards;\n	}\n\n	@keyframes swipe-out {\n		from {\n			transform: translateY(\n				calc(var(--lift) * var(--offset) + var(--swipe-amount))\n			);\n			opacity: 1;\n		}\n\n		to {\n			transform: translateY(\n				calc(\n					var(--lift) * var(--offset) + var(--swipe-amount) +\n						var(--lift) * -100%\n				)\n			);\n			opacity: 0;\n		}\n	}\n\n	@media (max-width: 600px) {\n		[data-sonner-toaster] {\n			position: fixed;\n			--mobile-offset: 16px;\n			right: var(--mobile-offset);\n			left: var(--mobile-offset);\n			width: 100%;\n		}\n\n		[data-sonner-toaster] [data-sonner-toast] {\n			left: 0;\n			right: 0;\n			width: calc(100% - var(--mobile-offset) * 2);\n		}\n\n		[data-sonner-toaster][data-x-position='left'] {\n			left: var(--mobile-offset);\n		}\n\n		[data-sonner-toaster][data-y-position='bottom'] {\n			bottom: 20px;\n		}\n\n		[data-sonner-toaster][data-y-position='top'] {\n			top: 20px;\n		}\n\n		[data-sonner-toaster][data-x-position='center'] {\n			left: var(--mobile-offset);\n			right: var(--mobile-offset);\n			transform: none;\n		}\n	}\n\n	[data-sonner-toaster][data-theme='light'] {\n		--normal-bg: #fff;\n		--normal-border: var(--gray4);\n		--normal-text: var(--gray12);\n\n		--success-bg: hsl(143, 85%, 96%);\n		--success-border: hsl(145, 92%, 91%);\n		--success-text: hsl(140, 100%, 27%);\n\n		--info-bg: hsl(208, 100%, 97%);\n		--info-border: hsl(221, 91%, 91%);\n		--info-text: hsl(210, 92%, 45%);\n\n		--warning-bg: hsl(49, 100%, 97%);\n		--warning-border: hsl(49, 91%, 91%);\n		--warning-text: hsl(31, 92%, 45%);\n\n		--error-bg: hsl(359, 100%, 97%);\n		--error-border: hsl(359, 100%, 94%);\n		--error-text: hsl(360, 100%, 45%);\n	}\n\n	[data-sonner-toaster][data-theme='light']\n		[data-sonner-toast][data-invert='true'] {\n		--normal-bg: #000;\n		--normal-border: hsl(0, 0%, 20%);\n		--normal-text: var(--gray1);\n	}\n\n	[data-sonner-toaster][data-theme='dark']\n		[data-sonner-toast][data-invert='true'] {\n		--normal-bg: #fff;\n		--normal-border: var(--gray3);\n		--normal-text: var(--gray12);\n	}\n\n	[data-sonner-toaster][data-theme='dark'] {\n		--normal-bg: #000;\n		--normal-border: hsl(0, 0%, 20%);\n		--normal-text: var(--gray1);\n\n		--success-bg: hsl(150, 100%, 6%);\n		--success-border: hsl(147, 100%, 12%);\n		--success-text: hsl(150, 86%, 65%);\n\n		--info-bg: hsl(215, 100%, 6%);\n		--info-border: hsl(223, 100%, 12%);\n		--info-text: hsl(216, 87%, 65%);\n\n		--warning-bg: hsl(64, 100%, 6%);\n		--warning-border: hsl(60, 100%, 12%);\n		--warning-text: hsl(46, 87%, 65%);\n\n		--error-bg: hsl(358, 76%, 10%);\n		--error-border: hsl(357, 89%, 16%);\n		--error-text: hsl(358, 100%, 81%);\n	}\n\n	[data-rich-colors='true'] [data-sonner-toast][data-type='success'] {\n		background: var(--success-bg);\n		border-color: var(--success-border);\n		color: var(--success-text);\n	}\n\n	[data-theme='dark']\n		[data-sonner-toast][data-type='default']\n		[data-close-button] {\n		background: var(--normal-bg);\n		border-color: var(--normal-border);\n		color: var(--normal-text);\n	}\n\n	[data-rich-colors='true']\n		[data-sonner-toast][data-type='success']\n		[data-close-button] {\n		background: var(--success-bg);\n		border-color: var(--success-border);\n		color: var(--success-text);\n	}\n\n	[data-rich-colors='true'] [data-sonner-toast][data-type='info'] {\n		background: var(--info-bg);\n		border-color: var(--info-border);\n		color: var(--info-text);\n	}\n\n	[data-rich-colors='true']\n		[data-sonner-toast][data-type='info']\n		[data-close-button] {\n		background: var(--info-bg);\n		border-color: var(--info-border);\n		color: var(--info-text);\n	}\n\n	[data-rich-colors='true'] [data-sonner-toast][data-type='warning'] {\n		background: var(--warning-bg);\n		border-color: var(--warning-border);\n		color: var(--warning-text);\n	}\n\n	[data-rich-colors='true']\n		[data-sonner-toast][data-type='warning']\n		[data-close-button] {\n		background: var(--warning-bg);\n		border-color: var(--warning-border);\n		color: var(--warning-text);\n	}\n\n	[data-rich-colors='true'] [data-sonner-toast][data-type='error'] {\n		background: var(--error-bg);\n		border-color: var(--error-border);\n		color: var(--error-text);\n	}\n\n	[data-rich-colors='true']\n		[data-sonner-toast][data-type='error']\n		[data-close-button] {\n		background: var(--error-bg);\n		border-color: var(--error-border);\n		color: var(--error-text);\n	}\n\n	.sonner-loading-wrapper {\n		--size: 16px;\n		height: var(--size);\n		width: var(--size);\n		position: absolute;\n		inset: 0;\n		z-index: 10;\n	}\n\n	.sonner-loading-wrapper[data-visible='false'] {\n		transform-origin: center;\n		animation: sonner-fade-out 0.2s ease forwards;\n	}\n\n	.sonner-spinner {\n		position: relative;\n		top: 50%;\n		left: 50%;\n		height: var(--size);\n		width: var(--size);\n	}\n\n	.sonner-loading-bar {\n		animation: sonner-spin 1.2s linear infinite;\n		background: var(--gray11);\n		border-radius: 6px;\n		height: 8%;\n		left: -10%;\n		position: absolute;\n		top: -3.9%;\n		width: 24%;\n	}\n\n	.sonner-loading-bar:nth-child(1) {\n		animation-delay: -1.2s;\n		transform: rotate(0.0001deg) translate(146%);\n	}\n\n	.sonner-loading-bar:nth-child(2) {\n		animation-delay: -1.1s;\n		transform: rotate(30deg) translate(146%);\n	}\n\n	.sonner-loading-bar:nth-child(3) {\n		animation-delay: -1s;\n		transform: rotate(60deg) translate(146%);\n	}\n\n	.sonner-loading-bar:nth-child(4) {\n		animation-delay: -0.9s;\n		transform: rotate(90deg) translate(146%);\n	}\n\n	.sonner-loading-bar:nth-child(5) {\n		animation-delay: -0.8s;\n		transform: rotate(120deg) translate(146%);\n	}\n\n	.sonner-loading-bar:nth-child(6) {\n		animation-delay: -0.7s;\n		transform: rotate(150deg) translate(146%);\n	}\n\n	.sonner-loading-bar:nth-child(7) {\n		animation-delay: -0.6s;\n		transform: rotate(180deg) translate(146%);\n	}\n\n	.sonner-loading-bar:nth-child(8) {\n		animation-delay: -0.5s;\n		transform: rotate(210deg) translate(146%);\n	}\n\n	.sonner-loading-bar:nth-child(9) {\n		animation-delay: -0.4s;\n		transform: rotate(240deg) translate(146%);\n	}\n\n	.sonner-loading-bar:nth-child(10) {\n		animation-delay: -0.3s;\n		transform: rotate(270deg) translate(146%);\n	}\n\n	.sonner-loading-bar:nth-child(11) {\n		animation-delay: -0.2s;\n		transform: rotate(300deg) translate(146%);\n	}\n\n	.sonner-loading-bar:nth-child(12) {\n		animation-delay: -0.1s;\n		transform: rotate(330deg) translate(146%);\n	}\n\n	@keyframes sonner-fade-in {\n		0% {\n			opacity: 0;\n			transform: scale(0.8);\n		}\n		100% {\n			opacity: 1;\n			transform: scale(1);\n		}\n	}\n\n	@keyframes sonner-fade-out {\n		0% {\n			opacity: 1;\n			transform: scale(1);\n		}\n		100% {\n			opacity: 0;\n			transform: scale(0.8);\n		}\n	}\n\n	@keyframes sonner-spin {\n		0% {\n			opacity: 1;\n		}\n		100% {\n			opacity: 0.15;\n		}\n	}\n\n	@media (prefers-reduced-motion) {\n		[data-sonner-toast],\n		[data-sonner-toast] > *,\n		.sonner-loading-bar {\n			transition: none !important;\n			animation: none !important;\n		}\n	}\n\n	.sonner-loader {\n		position: absolute;\n		top: 50%;\n		left: 50%;\n		transform: translate(-50%, -50%);\n		transform-origin: center;\n		transition:\n			opacity 200ms,\n			transform 200ms;\n	}\n\n	.sonner-loader[data-visible='false'] {\n		opacity: 0;\n		transform: scale(0.8) translate(-50%, -50%);\n	}\n\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiVG9hc3Rlci5zdmVsdGUiLCJtYXBwaW5ncyI6IiIsIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZXMiOlsiVG9hc3Rlci5zdmVsdGUiXX0= */"
};
function Toaster($$anchor, $$props) {
  check_target(new.target);
  const $$sanitized_props = legacy_rest_props($$props, [
    "children",
    "$$slots",
    "$$events",
    "$$legacy"
  ]);
  const $$restProps = legacy_rest_props($$sanitized_props, [
    "invert",
    "theme",
    "position",
    "hotkey",
    "containerAriaLabel",
    "richColors",
    "expand",
    "duration",
    "visibleToasts",
    "closeButton",
    "toastOptions",
    "offset",
    "dir"
  ]);
  push($$props, false, Toaster);
  append_styles($$anchor, $$css);
  const [$$stores, $$cleanup] = setup_stores();
  const $toasts = () => (validate_store(toasts, "toasts"), store_get(toasts, "$toasts", $$stores));
  const $heights = () => (validate_store(heights, "heights"), store_get(heights, "$heights", $$stores));
  const possiblePositions = mutable_source();
  const hotkeyLabel = mutable_source();
  const VISIBLE_TOASTS_AMOUNT = 3;
  const VIEWPORT_OFFSET = "32px";
  const TOAST_WIDTH = 356;
  const GAP = 14;
  const DARK = "dark";
  const LIGHT = "light";
  function getInitialTheme(t) {
    if (strict_equals(t, "system", false)) {
      return t;
    }
    if (strict_equals(typeof window, "undefined", false)) {
      if (window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches) {
        return DARK;
      }
      return LIGHT;
    }
    return LIGHT;
  }
  function getDocumentDirection() {
    if (strict_equals(typeof window, "undefined")) return "ltr";
    if (strict_equals(typeof document, "undefined")) return "ltr";
    const dirAttribute = document.documentElement.getAttribute("dir");
    if (strict_equals(dirAttribute, "auto") || !dirAttribute) {
      return window.getComputedStyle(document.documentElement).direction;
    }
    return dirAttribute;
  }
  let invert = prop($$props, "invert", 8, false);
  let theme = prop($$props, "theme", 8, "light");
  let position = prop($$props, "position", 8, "bottom-right");
  let hotkey = prop($$props, "hotkey", 24, () => ["altKey", "KeyT"]);
  let containerAriaLabel = prop($$props, "containerAriaLabel", 8, "Notifications");
  let richColors = prop($$props, "richColors", 8, false);
  let expand = prop($$props, "expand", 8, false);
  let duration = prop($$props, "duration", 8, 4e3);
  let visibleToasts = prop($$props, "visibleToasts", 8, VISIBLE_TOASTS_AMOUNT);
  let closeButton = prop($$props, "closeButton", 8, false);
  let toastOptions = prop($$props, "toastOptions", 24, () => ({}));
  let offset = prop($$props, "offset", 8, null);
  let dir = prop($$props, "dir", 24, getDocumentDirection);
  const { toasts, heights, reset: reset2 } = toastState;
  let expanded = mutable_source(false);
  let interacting = mutable_source(false);
  let actualTheme = mutable_source(getInitialTheme(theme()));
  let listRef = mutable_source();
  let lastFocusedElementRef = null;
  let isFocusWithinRef = false;
  onDestroy(() => {
    if (get(listRef) && lastFocusedElementRef) {
      lastFocusedElementRef.focus({ preventScroll: true });
      lastFocusedElementRef = null;
      isFocusWithinRef = false;
    }
  });
  onMount(() => {
    reset2();
    const handleKeydown = (event2) => {
      var _a, _b;
      const isHotkeyPressed = hotkey().every((key) => (
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        event2[key] || strict_equals(event2.code, key)
      ));
      if (isHotkeyPressed) {
        set(expanded, true);
        (_a = get(listRef)) == null ? void 0 : _a.focus();
      }
      if (strict_equals(event2.code, "Escape") && (strict_equals(document.activeElement, get(listRef)) || ((_b = get(listRef)) == null ? void 0 : _b.contains(document.activeElement)))) {
        set(expanded, false);
      }
    };
    document.addEventListener("keydown", handleKeydown);
    return () => {
      document.removeEventListener("keydown", handleKeydown);
    };
  });
  function handleBlur(event2) {
    if (isFocusWithinRef && !event2.currentTarget.contains(event2.relatedTarget)) {
      isFocusWithinRef = false;
      if (lastFocusedElementRef) {
        lastFocusedElementRef.focus({ preventScroll: true });
        lastFocusedElementRef = null;
      }
    }
  }
  function handleFocus(event2) {
    if (!isFocusWithinRef) {
      isFocusWithinRef = true;
      lastFocusedElementRef = event2.relatedTarget;
    }
  }
  legacy_pre_effect(
    () => (deep_read_state(position()), $toasts()),
    () => {
      set(possiblePositions, Array.from(new Set([
        position(),
        ...$toasts().filter((toast2) => toast2.position).map((toast2) => toast2.position)
      ].filter(Boolean))));
    }
  );
  legacy_pre_effect(() => deep_read_state(hotkey()), () => {
    set(hotkeyLabel, hotkey().join("+").replace(/Key/g, "").replace(/Digit/g, ""));
  });
  legacy_pre_effect(() => $toasts(), () => {
    if ($toasts().length <= 1) {
      set(expanded, false);
    }
  });
  legacy_pre_effect(() => $toasts(), () => {
    const toastsToDismiss = $toasts().filter((toast2) => toast2.dismiss && !toast2.delete);
    if (toastsToDismiss.length > 0) {
      const updatedToasts = $toasts().map((toast2) => {
        const matchingToast = toastsToDismiss.find((dismissToast) => strict_equals(dismissToast.id, toast2.id));
        if (matchingToast) {
          return { ...toast2, delete: true };
        }
        return toast2;
      });
      toasts.set(updatedToasts);
    }
  });
  legacy_pre_effect(() => deep_read_state(theme()), () => {
    if (strict_equals(theme(), "system", false)) {
      set(actualTheme, theme());
    }
    if (strict_equals(typeof window, "undefined", false)) {
      if (strict_equals(theme(), "system")) {
        if (window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches) {
          set(actualTheme, DARK);
        } else {
          set(actualTheme, LIGHT);
        }
      }
      const mediaQueryList = window.matchMedia("(prefers-color-scheme: dark)");
      const changeHandler = ({ matches }) => {
        set(actualTheme, matches ? DARK : LIGHT);
      };
      if ("addEventListener" in mediaQueryList) {
        mediaQueryList.addEventListener("change", changeHandler);
      } else {
        mediaQueryList.addListener(changeHandler);
      }
    }
  });
  legacy_pre_effect_reset();
  init();
  var fragment = comment();
  var node = first_child(fragment);
  {
    var consequent = ($$anchor2) => {
      var section = root_14();
      set_attribute(section, "tabindex", -1);
      each(section, 5, () => get(possiblePositions), index, ($$anchor3, position2, index2, $$array) => {
        var ol = root_2();
        let attributes;
        validate_each_keys(() => $toasts().filter((toast2) => !toast2.position && strict_equals(index2, 0) || strict_equals(toast2.position, get(position2))), (toast2) => toast2.id);
        each(ol, 7, () => $toasts().filter((toast2) => !toast2.position && strict_equals(index2, 0) || strict_equals(toast2.position, get(position2))), (toast2) => toast2.id, ($$anchor4, toast2, index3, $$array_1) => {
          var fragment_1 = comment();
          var node_1 = first_child(fragment_1);
          const expression = derived_safe_equal(() => {
            var _a;
            return ((_a = toastOptions()) == null ? void 0 : _a.actionButtonStyle) || "";
          });
          const expression_1 = derived_safe_equal(() => {
            var _a;
            return ((_a = toastOptions()) == null ? void 0 : _a.cancelButtonStyle) || "";
          });
          const expression_2 = derived_safe_equal(() => {
            var _a;
            return ((_a = toastOptions()) == null ? void 0 : _a.class) || "";
          });
          const expression_3 = derived_safe_equal(() => {
            var _a;
            return ((_a = toastOptions()) == null ? void 0 : _a.descriptionClass) || "";
          });
          const expression_4 = derived_safe_equal(() => toastOptions().classes || {});
          const expression_5 = derived_safe_equal(() => {
            var _a;
            return ((_a = toastOptions()) == null ? void 0 : _a.duration) ?? duration();
          });
          const expression_6 = derived_safe_equal(() => toastOptions().unstyled || false);
          Toast_default(node_1, {
            get index() {
              return get(index3);
            },
            get toast() {
              return get(toast2);
            },
            get invert() {
              return invert();
            },
            get visibleToasts() {
              return visibleToasts();
            },
            get closeButton() {
              return closeButton();
            },
            get interacting() {
              return get(interacting);
            },
            get position() {
              return get(position2);
            },
            get expandByDefault() {
              return expand();
            },
            get expanded() {
              return get(expanded);
            },
            get actionButtonStyle() {
              return get(expression);
            },
            get cancelButtonStyle() {
              return get(expression_1);
            },
            get class() {
              return get(expression_2);
            },
            get descriptionClass() {
              return get(expression_3);
            },
            get classes() {
              return get(expression_4);
            },
            get duration() {
              return get(expression_5);
            },
            get unstyled() {
              return get(expression_6);
            },
            $$slots: {
              "loading-icon": ($$anchor5, $$slotProps) => {
                var fragment_2 = comment();
                var node_2 = first_child(fragment_2);
                slot(node_2, $$props, "loading-icon", {}, ($$anchor6) => {
                  var fragment_3 = comment();
                  var node_3 = first_child(fragment_3);
                  const expression_7 = derived_safe_equal(() => strict_equals(get(toast2).type, "loading"));
                  Loader_default(node_3, {
                    get visible() {
                      return get(expression_7);
                    }
                  });
                  append($$anchor6, fragment_3);
                });
                append($$anchor5, fragment_2);
              },
              "success-icon": ($$anchor5, $$slotProps) => {
                var fragment_4 = comment();
                var node_4 = first_child(fragment_4);
                slot(node_4, $$props, "success-icon", {}, ($$anchor6) => {
                  var fragment_5 = comment();
                  var node_5 = first_child(fragment_5);
                  Icon_default(node_5, { type: "success" });
                  append($$anchor6, fragment_5);
                });
                append($$anchor5, fragment_4);
              },
              "error-icon": ($$anchor5, $$slotProps) => {
                var fragment_6 = comment();
                var node_6 = first_child(fragment_6);
                slot(node_6, $$props, "error-icon", {}, ($$anchor6) => {
                  var fragment_7 = comment();
                  var node_7 = first_child(fragment_7);
                  Icon_default(node_7, { type: "error" });
                  append($$anchor6, fragment_7);
                });
                append($$anchor5, fragment_6);
              },
              "warning-icon": ($$anchor5, $$slotProps) => {
                var fragment_8 = comment();
                var node_8 = first_child(fragment_8);
                slot(node_8, $$props, "warning-icon", {}, ($$anchor6) => {
                  var fragment_9 = comment();
                  var node_9 = first_child(fragment_9);
                  Icon_default(node_9, { type: "warning" });
                  append($$anchor6, fragment_9);
                });
                append($$anchor5, fragment_8);
              },
              "info-icon": ($$anchor5, $$slotProps) => {
                var fragment_10 = comment();
                var node_10 = first_child(fragment_10);
                slot(node_10, $$props, "info-icon", {}, ($$anchor6) => {
                  var fragment_11 = comment();
                  var node_11 = first_child(fragment_11);
                  Icon_default(node_11, { type: "info" });
                  append($$anchor6, fragment_11);
                });
                append($$anchor5, fragment_10);
              }
            }
          });
          append($$anchor4, fragment_1);
        });
        reset(ol);
        bind_this(ol, ($$value) => set(listRef, $$value), () => get(listRef));
        template_effect(
          ($0, $1, $2) => {
            var _a;
            attributes = set_attributes(
              ol,
              attributes,
              {
                tabIndex: -1,
                class: $$sanitized_props.class,
                "data-sonner-toaster": true,
                "data-theme": get(actualTheme),
                "data-rich-colors": richColors(),
                dir: $0,
                "data-y-position": $1,
                "data-x-position": $2,
                style: $$sanitized_props.style,
                ...$$restProps,
                [STYLE]: {
                  "--front-toast-height": `${(_a = $heights()[0]) == null ? void 0 : _a.height}px`,
                  "--offset": strict_equals(typeof offset(), "number") ? `${offset()}px` : offset() || VIEWPORT_OFFSET,
                  "--width": `${TOAST_WIDTH}px`,
                  "--gap": `${GAP}px`
                }
              },
              "s-KjATBeAaNTDC"
            );
            ol.dir = ol.dir;
          },
          [
            () => strict_equals(dir(), "auto") ? getDocumentDirection() : dir(),
            () => get(position2).split("-")[0],
            () => get(position2).split("-")[1]
          ],
          derived_safe_equal
        );
        event("blur", ol, handleBlur);
        event("focus", ol, handleFocus);
        event("mouseenter", ol, () => set(expanded, true));
        event("mousemove", ol, () => set(expanded, true));
        event("mouseleave", ol, () => {
          if (!get(interacting)) {
            set(expanded, false);
          }
        });
        event("pointerdown", ol, () => set(interacting, true));
        event("pointerup", ol, () => set(interacting, false));
        append($$anchor3, ol);
      });
      reset(section);
      template_effect(() => set_attribute(section, "aria-label", `${containerAriaLabel()} ${get(hotkeyLabel)}`));
      append($$anchor2, section);
    };
    if_block(node, ($$render) => {
      if ($toasts().length > 0) $$render(consequent);
    });
  }
  append($$anchor, fragment);
  var $$pop = pop({ ...legacy_api() });
  $$cleanup();
  return $$pop;
}
if (import.meta.hot) {
  Toaster = hmr(Toaster, () => Toaster[HMR].source);
  import.meta.hot.accept((module) => {
    cleanup_styles("s-KjATBeAaNTDC");
    module.default[HMR].source = Toaster[HMR].source;
    set(Toaster[HMR].source, module.default[HMR].original);
  });
}
var Toaster_default = Toaster;
export {
  Icon_default as Icon,
  Loader_default as Loader,
  Toaster_default as Toaster,
  toast
};
//# sourceMappingURL=svelte-sonner.js.map
