import { json } from '@sveltejs/kit';
import { getUserFromToken } from '$lib/server/auth';
import { prisma } from '$lib/server/prisma';
import type { RequestHandler } from './$types';

/**
 * Subscribe user to push notifications
 */
export const POST: RequestHandler = async ({ request, cookies }) => {
  try {
    // Authenticate the user
    const user = await getUserFromToken(cookies);
    if (!user) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse the push subscription data
    const subscription = await request.json();

    if (!subscription || !subscription.endpoint) {
      return json({ error: 'Invalid subscription data' }, { status: 400 });
    }

    // Check if subscription already exists
    const existingSubscription = await prisma.pushSubscription.findFirst({
      where: {
        userId: user.id,
        endpoint: subscription.endpoint,
      },
    });

    if (existingSubscription) {
      // Update existing subscription
      await prisma.pushSubscription.update({
        where: { id: existingSubscription.id },
        data: {
          p256dh: subscription.keys?.p256dh,
          auth: subscription.keys?.auth,
          updatedAt: new Date(),
        },
      });
    } else {
      // Create new subscription
      await prisma.pushSubscription.create({
        data: {
          userId: user.id,
          endpoint: subscription.endpoint,
          p256dh: subscription.keys?.p256dh,
          auth: subscription.keys?.auth,
        },
      });
    }

    // Update user's push notification preference
    await prisma.notificationSettings.upsert({
      where: { userId: user.id },
      update: { pushEnabled: true },
      create: {
        userId: user.id,
        pushEnabled: true,
        emailEnabled: true,
        browserEnabled: true,
        jobMatchEnabled: true,
        applicationStatusEnabled: true,
        automationEnabled: true,
      },
    });

    console.log(`Push subscription saved for user ${user.id}`);
    return json({ success: true });
  } catch (error) {
    console.error('Error saving push subscription:', error);
    return json({ error: 'Failed to save subscription' }, { status: 500 });
  }
};
