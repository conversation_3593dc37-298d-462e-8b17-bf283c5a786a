import "./chunk-AG62LWSR.js";
import "./chunk-AGPRHSYH.js";
import "./chunk-RIXFT5AQ.js";
import {
  add_locations,
  check_target,
  derived,
  get as get2,
  hmr,
  html,
  if_block,
  init,
  legacy_api,
  onMount,
  prop,
  set_attribute,
  setup_stores,
  store_get,
  validate_store,
  writable
} from "./chunk-NDLSR7SS.js";
import "./chunk-U7P2NEEE.js";
import {
  append,
  comment,
  head,
  template
} from "./chunk-4ZPQDFFK.js";
import {
  FILENAME,
  HMR,
  deep_read_state,
  first_child,
  get,
  legacy_pre_effect,
  legacy_pre_effect_reset,
  mutable_source,
  pop,
  push,
  set,
  sibling,
  strict_equals,
  template_effect
} from "./chunk-QYXENPIM.js";
import "./chunk-VIZMNZTH.js";
import "./chunk-HNWPC2PS.js";
import "./chunk-C5KNTEDU.js";
import "./chunk-RVAV4ZRS.js";
import "./chunk-KWPVD4H7.js";

// node_modules/mode-watcher/dist/without-transition.js
var timeoutAction;
var timeoutEnable;
function withoutTransition(action) {
  if (typeof document === "undefined")
    return;
  clearTimeout(timeoutAction);
  clearTimeout(timeoutEnable);
  const style = document.createElement("style");
  const css = document.createTextNode(`* {
     -webkit-transition: none !important;
     -moz-transition: none !important;
     -o-transition: none !important;
     -ms-transition: none !important;
     transition: none !important;
  }`);
  style.appendChild(css);
  const disable = () => document.head.appendChild(style);
  const enable = () => document.head.removeChild(style);
  if (typeof window.getComputedStyle !== "undefined") {
    disable();
    action();
    window.getComputedStyle(style).opacity;
    enable();
    return;
  }
  if (typeof window.requestAnimationFrame !== "undefined") {
    disable();
    action();
    window.requestAnimationFrame(enable);
    return;
  }
  disable();
  timeoutAction = window.setTimeout(() => {
    action();
    timeoutEnable = window.setTimeout(enable, 120);
  }, 120);
}

// node_modules/mode-watcher/dist/utils.js
function sanitizeClassNames(classNames) {
  return classNames.filter((className) => className.length > 0);
}

// node_modules/mode-watcher/dist/stores.js
var noopStorage = {
  getItem: (_key) => null,
  setItem: (_key, _value) => {
  }
};
var isBrowser = typeof document !== "undefined";
var modes = ["dark", "light", "system"];
var modeStorageKey = writable("mode-watcher-mode");
var themeStorageKey = writable("mode-watcher-theme");
var userPrefersMode = createUserPrefersMode();
var systemPrefersMode = createSystemMode();
var themeColors = writable(void 0);
var theme = createCustomTheme();
var disableTransitions = writable(true);
var darkClassNames = writable([]);
var lightClassNames = writable([]);
var derivedMode = createDerivedMode();
var derivedTheme = createDerivedTheme();
function createUserPrefersMode() {
  const defaultValue = "system";
  const storage = isBrowser ? localStorage : noopStorage;
  const initialValue = storage.getItem(getModeStorageKey());
  let value = isValidMode(initialValue) ? initialValue : defaultValue;
  function getModeStorageKey() {
    return get2(modeStorageKey);
  }
  const { subscribe, set: _set } = writable(value, () => {
    if (!isBrowser)
      return;
    const handler = (e) => {
      if (e.key !== getModeStorageKey())
        return;
      const newValue = e.newValue;
      if (isValidMode(newValue)) {
        _set(value = newValue);
      } else {
        _set(value = defaultValue);
      }
    };
    addEventListener("storage", handler);
    return () => removeEventListener("storage", handler);
  });
  function set2(v) {
    _set(value = v);
    storage.setItem(getModeStorageKey(), value);
  }
  return {
    subscribe,
    set: set2
  };
}
function createCustomTheme() {
  const storage = isBrowser ? localStorage : noopStorage;
  const initialValue = storage.getItem(getThemeStorageKey());
  let value = initialValue === null || initialValue === void 0 ? "" : initialValue;
  function getThemeStorageKey() {
    return get2(themeStorageKey);
  }
  const { subscribe, set: _set } = writable(value, () => {
    if (!isBrowser)
      return;
    const handler = (e) => {
      if (e.key !== getThemeStorageKey())
        return;
      const newValue = e.newValue;
      if (newValue === null) {
        _set(value = "");
      } else {
        _set(value = newValue);
      }
    };
    addEventListener("storage", handler);
    return () => removeEventListener("storage", handler);
  });
  function set2(v) {
    _set(value = v);
    storage.setItem(getThemeStorageKey(), value);
  }
  return {
    subscribe,
    set: set2
  };
}
function createSystemMode() {
  const defaultValue = void 0;
  let track = true;
  const { subscribe, set: set2 } = writable(defaultValue, () => {
    if (!isBrowser)
      return;
    const handler = (e) => {
      if (!track)
        return;
      set2(e.matches ? "light" : "dark");
    };
    const mediaQueryState = window.matchMedia("(prefers-color-scheme: light)");
    mediaQueryState.addEventListener("change", handler);
    return () => mediaQueryState.removeEventListener("change", handler);
  });
  function query() {
    if (!isBrowser)
      return;
    const mediaQueryState = window.matchMedia("(prefers-color-scheme: light)");
    set2(mediaQueryState.matches ? "light" : "dark");
  }
  function tracking(active) {
    track = active;
  }
  return {
    subscribe,
    query,
    tracking
  };
}
function createDerivedMode() {
  const { subscribe } = derived([
    userPrefersMode,
    systemPrefersMode,
    themeColors,
    disableTransitions,
    darkClassNames,
    lightClassNames
  ], ([$userPrefersMode, $systemPrefersMode, $themeColors, $disableTransitions, $darkClassNames, $lightClassNames]) => {
    if (!isBrowser)
      return void 0;
    const derivedMode2 = $userPrefersMode === "system" ? $systemPrefersMode : $userPrefersMode;
    const sanitizedDarkClassNames = sanitizeClassNames($darkClassNames);
    const sanitizedLightClassNames = sanitizeClassNames($lightClassNames);
    function update() {
      const htmlEl = document.documentElement;
      const themeColorEl = document.querySelector('meta[name="theme-color"]');
      if (derivedMode2 === "light") {
        if (sanitizedDarkClassNames.length)
          htmlEl.classList.remove(...sanitizedDarkClassNames);
        if (sanitizedLightClassNames.length)
          htmlEl.classList.add(...sanitizedLightClassNames);
        htmlEl.style.colorScheme = "light";
        if (themeColorEl && $themeColors) {
          themeColorEl.setAttribute("content", $themeColors.light);
        }
      } else {
        if (sanitizedLightClassNames.length)
          htmlEl.classList.remove(...sanitizedLightClassNames);
        if (sanitizedDarkClassNames.length)
          htmlEl.classList.add(...sanitizedDarkClassNames);
        htmlEl.style.colorScheme = "dark";
        if (themeColorEl && $themeColors) {
          themeColorEl.setAttribute("content", $themeColors.dark);
        }
      }
    }
    if ($disableTransitions) {
      withoutTransition(update);
    } else {
      update();
    }
    return derivedMode2;
  });
  return {
    subscribe
  };
}
function createDerivedTheme() {
  const { subscribe } = derived([theme, disableTransitions], ([$theme, $disableTransitions]) => {
    if (!isBrowser)
      return void 0;
    function update() {
      const htmlEl = document.documentElement;
      htmlEl.setAttribute("data-theme", $theme);
    }
    if ($disableTransitions) {
      withoutTransition(update);
    } else {
      update();
    }
    return $theme;
  });
  return {
    subscribe
  };
}
function isValidMode(value) {
  if (typeof value !== "string")
    return false;
  return modes.includes(value);
}

// node_modules/mode-watcher/dist/mode.js
function toggleMode() {
  userPrefersMode.set(get2(derivedMode) === "dark" ? "light" : "dark");
}
function setMode(mode) {
  userPrefersMode.set(mode);
}
function resetMode() {
  userPrefersMode.set("system");
}
function setTheme(theme2) {
  theme.set(theme2);
}
function defineConfig(config) {
  return config;
}
function setInitialMode({ defaultMode = "system", themeColors: themeColors2, darkClassNames: darkClassNames2 = ["dark"], lightClassNames: lightClassNames2 = [], defaultTheme = "", modeStorageKey: modeStorageKey2 = "mode-watcher-mode", themeStorageKey: themeStorageKey2 = "mode-watcher-theme" }) {
  const rootEl = document.documentElement;
  const mode = localStorage.getItem(modeStorageKey2) || defaultMode;
  const theme2 = localStorage.getItem(themeStorageKey2) || defaultTheme;
  const light = mode === "light" || mode === "system" && window.matchMedia("(prefers-color-scheme: light)").matches;
  if (light) {
    if (darkClassNames2.length)
      rootEl.classList.remove(...darkClassNames2);
    if (lightClassNames2.length)
      rootEl.classList.add(...lightClassNames2);
  } else {
    if (lightClassNames2.length)
      rootEl.classList.remove(...lightClassNames2);
    if (darkClassNames2.length)
      rootEl.classList.add(...darkClassNames2);
  }
  rootEl.style.colorScheme = light ? "light" : "dark";
  if (themeColors2) {
    const themeMetaEl = document.querySelector('meta[name="theme-color"]');
    if (themeMetaEl) {
      themeMetaEl.setAttribute("content", mode === "light" ? themeColors2.light : themeColors2.dark);
    }
  }
  if (theme2) {
    rootEl.setAttribute("data-theme", theme2);
    localStorage.setItem(themeStorageKey2, theme2);
  }
  localStorage.setItem(modeStorageKey2, mode);
}
function generateSetInitialModeExpression(config = {}) {
  return `(${setInitialMode.toString()})(${JSON.stringify(config)});`;
}

// node_modules/mode-watcher/dist/mode-watcher-lite.svelte
Mode_watcher_lite[FILENAME] = "node_modules/mode-watcher/dist/mode-watcher-lite.svelte";
var root_1 = add_locations(template(`<meta name="theme-color">`), Mode_watcher_lite[FILENAME], [[8, 1]]);
function Mode_watcher_lite($$anchor, $$props) {
  check_target(new.target);
  push($$props, false, Mode_watcher_lite);
  let themeColors2 = prop($$props, "themeColors", 24, () => void 0);
  init();
  var fragment = comment();
  var node = first_child(fragment);
  {
    var consequent = ($$anchor2) => {
      var meta = root_1();
      template_effect(() => set_attribute(meta, "content", themeColors2().dark));
      append($$anchor2, meta);
    };
    if_block(node, ($$render) => {
      if (themeColors2()) $$render(consequent);
    });
  }
  append($$anchor, fragment);
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  Mode_watcher_lite = hmr(Mode_watcher_lite, () => Mode_watcher_lite[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Mode_watcher_lite[HMR].source;
    set(Mode_watcher_lite[HMR].source, module.default[HMR].original);
  });
}
var mode_watcher_lite_default = Mode_watcher_lite;

// node_modules/mode-watcher/dist/mode-watcher-full.svelte
Mode_watcher_full[FILENAME] = "node_modules/mode-watcher/dist/mode-watcher-full.svelte";
var root_2 = add_locations(template(`<meta name="theme-color">`), Mode_watcher_full[FILENAME], [[12, 2]]);
var root_12 = add_locations(template(`<!> <!>`, 1), Mode_watcher_full[FILENAME], []);
function Mode_watcher_full($$anchor, $$props) {
  check_target(new.target);
  push($$props, false, Mode_watcher_full);
  let trueNonce = prop($$props, "trueNonce", 8, "");
  let initConfig = prop($$props, "initConfig", 8);
  let themeColors2 = prop($$props, "themeColors", 24, () => void 0);
  init();
  head(($$anchor2) => {
    var fragment = root_12();
    var node = first_child(fragment);
    {
      var consequent = ($$anchor3) => {
        var meta = root_2();
        template_effect(() => set_attribute(meta, "content", themeColors2().dark));
        append($$anchor3, meta);
      };
      if_block(node, ($$render) => {
        if (themeColors2()) $$render(consequent);
      });
    }
    var node_1 = sibling(node, 2);
    html(node_1, () => `<script${trueNonce() ? ` nonce=${trueNonce()}` : ""}>(` + setInitialMode.toString() + `)(` + JSON.stringify(initConfig()) + `);<\/script>`, false, false, true);
    append($$anchor2, fragment);
  });
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  Mode_watcher_full = hmr(Mode_watcher_full, () => Mode_watcher_full[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Mode_watcher_full[HMR].source;
    set(Mode_watcher_full[HMR].source, module.default[HMR].original);
  });
}
var mode_watcher_full_default = Mode_watcher_full;

// node_modules/mode-watcher/dist/mode-watcher.svelte
Mode_watcher[FILENAME] = "node_modules/mode-watcher/dist/mode-watcher.svelte";
function Mode_watcher($$anchor, $$props) {
  check_target(new.target);
  push($$props, false, Mode_watcher);
  const [$$stores, $$cleanup] = setup_stores();
  const $modeStorageKeyStore = () => (validate_store(modeStorageKey, "modeStorageKeyStore"), store_get(modeStorageKey, "$modeStorageKeyStore", $$stores));
  const $themeStorageKeyStore = () => (validate_store(themeStorageKey, "themeStorageKeyStore"), store_get(themeStorageKey, "$themeStorageKeyStore", $$stores));
  const trueNonce = mutable_source();
  let track = prop($$props, "track", 8, true);
  let defaultMode = prop($$props, "defaultMode", 8, "system");
  let themeColors2 = prop($$props, "themeColors", 24, () => void 0);
  let disableTransitions2 = prop($$props, "disableTransitions", 8, true);
  let darkClassNames2 = prop($$props, "darkClassNames", 24, () => ["dark"]);
  let lightClassNames2 = prop($$props, "lightClassNames", 24, () => []);
  let defaultTheme = prop($$props, "defaultTheme", 8, "");
  let nonce = prop($$props, "nonce", 8, "");
  let themeStorageKey2 = prop($$props, "themeStorageKey", 8, "mode-watcher-theme");
  let modeStorageKey2 = prop($$props, "modeStorageKey", 8, "mode-watcher-mode");
  let disableHeadScriptInjection = prop($$props, "disableHeadScriptInjection", 8, false);
  onMount(() => {
    const modeUnsubscribe = derivedMode.subscribe(() => {
    });
    const themeUnsubscribe = derivedTheme.subscribe(() => {
    });
    systemPrefersMode.tracking(track());
    systemPrefersMode.query();
    const localStorageMode = localStorage.getItem($modeStorageKeyStore());
    setMode(isValidMode(localStorageMode) ? localStorageMode : defaultMode());
    const localStorageTheme = localStorage.getItem($themeStorageKeyStore());
    setTheme(localStorageTheme || defaultTheme());
    return () => {
      modeUnsubscribe();
      themeUnsubscribe();
    };
  });
  const initConfig = defineConfig({
    defaultMode: defaultMode(),
    themeColors: themeColors2(),
    darkClassNames: darkClassNames2(),
    lightClassNames: lightClassNames2(),
    defaultTheme: defaultTheme(),
    modeStorageKey: modeStorageKey2(),
    themeStorageKey: themeStorageKey2()
  });
  legacy_pre_effect(
    () => (disableTransitions, deep_read_state(disableTransitions2())),
    () => {
      disableTransitions.set(disableTransitions2());
    }
  );
  legacy_pre_effect(
    () => (themeColors, deep_read_state(themeColors2())),
    () => {
      themeColors.set(themeColors2());
    }
  );
  legacy_pre_effect(
    () => (darkClassNames, deep_read_state(darkClassNames2())),
    () => {
      darkClassNames.set(darkClassNames2());
    }
  );
  legacy_pre_effect(
    () => (lightClassNames, deep_read_state(lightClassNames2())),
    () => {
      lightClassNames.set(lightClassNames2());
    }
  );
  legacy_pre_effect(
    () => (modeStorageKey, deep_read_state(modeStorageKey2())),
    () => {
      modeStorageKey.set(modeStorageKey2());
    }
  );
  legacy_pre_effect(
    () => (themeStorageKey, deep_read_state(themeStorageKey2())),
    () => {
      themeStorageKey.set(themeStorageKey2());
    }
  );
  legacy_pre_effect(() => deep_read_state(nonce()), () => {
    set(trueNonce, strict_equals(typeof window, "undefined") ? nonce() : "");
  });
  legacy_pre_effect_reset();
  init();
  var fragment = comment();
  var node = first_child(fragment);
  {
    var consequent = ($$anchor2) => {
      var fragment_1 = comment();
      var node_1 = first_child(fragment_1);
      mode_watcher_lite_default(node_1, {
        get themeColors() {
          return themeColors2();
        }
      });
      append($$anchor2, fragment_1);
    };
    var alternate = ($$anchor2) => {
      var fragment_2 = comment();
      var node_2 = first_child(fragment_2);
      mode_watcher_full_default(node_2, {
        get trueNonce() {
          return get(trueNonce);
        },
        initConfig,
        get themeColors() {
          return themeColors2();
        }
      });
      append($$anchor2, fragment_2);
    };
    if_block(node, ($$render) => {
      if (disableHeadScriptInjection()) $$render(consequent);
      else $$render(alternate, false);
    });
  }
  append($$anchor, fragment);
  var $$pop = pop({ ...legacy_api() });
  $$cleanup();
  return $$pop;
}
if (import.meta.hot) {
  Mode_watcher = hmr(Mode_watcher, () => Mode_watcher[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Mode_watcher[HMR].source;
    set(Mode_watcher[HMR].source, module.default[HMR].original);
  });
}
var mode_watcher_default = Mode_watcher;
export {
  mode_watcher_default as ModeWatcher,
  generateSetInitialModeExpression,
  derivedMode as mode,
  modeStorageKey,
  resetMode,
  setMode,
  setTheme,
  systemPrefersMode,
  derivedTheme as theme,
  themeStorageKey,
  toggleMode,
  userPrefersMode
};
//# sourceMappingURL=mode-watcher.js.map
