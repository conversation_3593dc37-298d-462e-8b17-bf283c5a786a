import { createClient } from 'redis';
import { getUserFromToken } from '$lib/server/auth.js';
import type { RequestHandler } from './$types';

// WebSocket endpoint for automation updates
export const GET: RequestHandler = async ({ cookies, request }) => {
  const user = getUserFromToken(cookies);

  if (!user) {
    return new Response('Unauthorized', { status: 401 });
  }

  // Create WebSocket connection
  const { socket, response } = Bun.upgradeWebSocket(request);

  // Connect to Redis
  const redisUrl =
    process.env.NODE_ENV === 'production'
      ? 'rediss://red-cvmu1me3jp1c738ve7ig:<EMAIL>:6379'
      : 'redis://localhost:6379';

  const redis = createClient({
    url: redisUrl,
  });

  const subscriber = redis.duplicate();

  // Handle WebSocket events
  socket.onopen = async () => {
    console.log('WebSocket connection opened');

    try {
      await redis.connect();
      await subscriber.connect();

      // Subscribe to automation status updates
      await subscriber.subscribe('automation:status', async (message) => {
        try {
          const data = JSON.parse(message);

          // Only send updates for this user's runs
          if (data.userId === user.id) {
            socket.send(
              JSON.stringify({
                type: 'status',
                data,
              })
            );

            // Notifications are now handled by the automation worker via Redis stream
          }
        } catch (error) {
          console.error('Error processing message:', error);
        }
      });

      // Subscribe to job updates
      await subscriber.subscribe('automation:jobs', (message) => {
        try {
          const data = JSON.parse(message);

          // Only send updates for this user's runs
          if (data.userId === user.id) {
            socket.send(
              JSON.stringify({
                type: 'jobs',
                data,
              })
            );
          }
        } catch (error) {
          console.error('Error processing message:', error);
        }
      });

      console.log('Subscribed to Redis channels');
    } catch (error) {
      console.error('Error connecting to Redis:', error);
      socket.close();
    }
  };

  socket.onclose = async () => {
    console.log('WebSocket connection closed');

    // Clean up Redis connections
    try {
      await subscriber.quit();
      await redis.quit();
    } catch (error) {
      console.error('Error closing Redis connections:', error);
    }
  };

  socket.onerror = (error) => {
    console.error('WebSocket error:', error);
  };

  socket.onmessage = (event) => {
    try {
      const message = JSON.parse(event.data);
      console.log('Received message:', message);

      // Handle client messages if needed
    } catch (error) {
      console.error('Error processing client message:', error);
    }
  };

  return response;
};
