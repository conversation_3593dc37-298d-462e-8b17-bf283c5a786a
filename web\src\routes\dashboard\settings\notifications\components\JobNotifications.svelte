<script lang="ts">
  import * as Switch from '$lib/components/ui/switch/index.js';
  import * as Select from '$lib/components/ui/select/index.js';
  import type { SuperForm } from 'sveltekit-superforms';
  import type { z } from 'zod';
  import type { notificationFormSchema } from '$lib/schemas/notification-schema';

  export let formData: SuperForm<z.infer<typeof notificationFormSchema>>;

  // Extract the form data store from the SuperForm
  const { form } = formData;

  // Function to trigger form change event
  function triggerFormChange() {
    // Dispatch a change event to the parent form
    const formElement = document.getElementById('notification-form');
    if (formElement) {
      formElement.dispatchEvent(new Event('change', { bubbles: true }));
    }
  }

  // Using direct form access with SuperForms
</script>

<div class="space-y-6">
  <div>
    <h3 class="text-lg font-medium">Job Alert Notifications</h3>
    <p class="text-muted-foreground text-sm">Configure notifications for job-related activities.</p>
  </div>
  <div class="space-y-6">
    <!-- Job Match Notifications -->
    <div class="flex items-center justify-between">
      <div class="space-y-0.5">
        <div class="font-medium">Job Match Notifications</div>
        <div class="text-muted-foreground text-sm">
          Receive notifications when new job matches are found
        </div>
      </div>
      <Switch.Root
        checked={Boolean($form.jobMatchNotifications)}
        onCheckedChange={(checked) => {
          form.update((f) => ({ ...f, jobMatchNotifications: checked }));
          triggerFormChange();
        }} />
    </div>

    {#if Boolean($form.jobMatchNotifications)}
      <div class="border-muted mb-6 mt-4 border-l-2 pl-6">
        <div class="space-y-2">
          <div class="font-medium">Notification Frequency</div>
          <Select.Root
            type="single"
            value={$form.jobMatchFrequency || 'daily'}
            onValueChange={(value: 'realtime' | 'daily' | 'weekly') => {
              form.update((f) => ({
                ...f,
                jobMatchFrequency: value,
              }));
              triggerFormChange();
            }}>
            <Select.Trigger class="w-full">
              {$form.jobMatchFrequency === 'realtime'
                ? 'Real-time'
                : $form.jobMatchFrequency === 'daily'
                  ? 'Daily Digest'
                  : $form.jobMatchFrequency === 'weekly'
                    ? 'Weekly Digest'
                    : 'Select frequency'}
            </Select.Trigger>
            <Select.Content class="max-h-60">
              <Select.Item value="realtime">Real-time</Select.Item>
              <Select.Item value="daily">Daily Digest</Select.Item>
              <Select.Item value="weekly">Weekly Digest</Select.Item>
            </Select.Content>
          </Select.Root>
          <div class="text-muted-foreground text-sm">
            How often you want to receive job match notifications
          </div>
        </div>
      </div>
    {/if}

    <!-- Application Status Updates -->
    <div class="flex items-center justify-between">
      <div class="space-y-0.5">
        <div class="font-medium">Application Status Updates</div>
        <div class="text-muted-foreground text-sm">
          Receive notifications when your application status changes
        </div>
      </div>
      <Switch.Root
        checked={Boolean($form.applicationStatusNotifications)}
        onCheckedChange={(checked) => {
          form.update((f) => ({ ...f, applicationStatusNotifications: checked }));
          triggerFormChange();
        }} />
    </div>

    <!-- New Jobs Notifications -->
    <div class="flex items-center justify-between">
      <div class="space-y-0.5">
        <div class="font-medium">New Jobs Notifications</div>
        <div class="text-muted-foreground text-sm">
          Receive notifications when new jobs matching your criteria are posted
        </div>
      </div>
      <Switch.Root
        checked={Boolean($form.newJobsNotifications)}
        onCheckedChange={(checked) => {
          form.update((f) => ({ ...f, newJobsNotifications: checked }));
          triggerFormChange();
        }} />
    </div>

    {#if Boolean($form.newJobsNotifications)}
      <div class="border-muted mb-6 mt-4 border-l-2 pl-6">
        <div class="space-y-2">
          <div class="font-medium">Notification Frequency</div>
          <Select.Root
            type="single"
            value={$form.newJobsFrequency || 'daily'}
            onValueChange={(value: 'realtime' | 'daily' | 'weekly') => {
              form.update((f) => ({
                ...f,
                newJobsFrequency: value,
              }));
              triggerFormChange();
            }}>
            <Select.Trigger class="w-full">
              {$form.newJobsFrequency === 'realtime'
                ? 'Real-time'
                : $form.newJobsFrequency === 'daily'
                  ? 'Daily Digest'
                  : $form.newJobsFrequency === 'weekly'
                    ? 'Weekly Digest'
                    : 'Select frequency'}
            </Select.Trigger>
            <Select.Content class="max-h-60">
              <Select.Item value="realtime">Real-time</Select.Item>
              <Select.Item value="daily">Daily Digest</Select.Item>
              <Select.Item value="weekly">Weekly Digest</Select.Item>
            </Select.Content>
          </Select.Root>
          <div class="text-muted-foreground text-sm">
            How often you want to receive new job notifications
          </div>
        </div>
      </div>
    {/if}

    <!-- Interview Reminders -->
    <div class="flex items-center justify-between">
      <div class="space-y-0.5">
        <div class="font-medium">Interview Reminders</div>
        <div class="text-muted-foreground text-sm">
          Receive reminders about upcoming interviews and follow-ups
        </div>
      </div>
      <Switch.Root
        checked={Boolean($form.interviewReminders)}
        onCheckedChange={(checked) => {
          form.update((f) => ({ ...f, interviewReminders: checked }));
          triggerFormChange();
        }} />
    </div>

    <!-- Saved Jobs Updates -->
    <div class="flex items-center justify-between">
      <div class="space-y-0.5">
        <div class="font-medium">Saved Jobs Updates</div>
        <div class="text-muted-foreground text-sm">
          Receive updates about jobs you've saved (closing dates, changes, etc.)
        </div>
      </div>
      <Switch.Root
        checked={Boolean($form.savedJobsUpdates)}
        onCheckedChange={(checked) => {
          form.update((f) => ({ ...f, savedJobsUpdates: checked }));
          triggerFormChange();
        }} />
    </div>

    <!-- Automation Notifications -->
    <div class="flex items-center justify-between">
      <div class="space-y-0.5">
        <div class="font-medium">Automation Notifications</div>
        <div class="text-muted-foreground text-sm">
          Receive notifications about automation runs, status updates, and results
        </div>
      </div>
      <Switch.Root
        checked={Boolean($form.automationNotifications)}
        onCheckedChange={(checked) => {
          form.update((f) => ({ ...f, automationNotifications: checked }));
          triggerFormChange();
        }} />
    </div>

    {#if Boolean($form.automationNotifications)}
      <div class="border-muted mb-6 mt-4 border-l-2 pl-6">
        <div class="space-y-2">
          <div class="font-medium">Notification Frequency</div>
          <Select.Root
            type="single"
            value={$form.automationFrequency || 'realtime'}
            onValueChange={(value: 'realtime' | 'daily' | 'weekly') => {
              form.update((f) => ({
                ...f,
                automationFrequency: value,
              }));
              triggerFormChange();
            }}>
            <Select.Trigger class="w-full">
              {$form.automationFrequency === 'realtime'
                ? 'Real-time'
                : $form.automationFrequency === 'daily'
                  ? 'Daily Digest'
                  : $form.automationFrequency === 'weekly'
                    ? 'Weekly Digest'
                    : 'Select frequency'}
            </Select.Trigger>
            <Select.Content class="max-h-60">
              <Select.Item value="realtime">Real-time</Select.Item>
              <Select.Item value="daily">Daily Digest</Select.Item>
              <Select.Item value="weekly">Weekly Digest</Select.Item>
            </Select.Content>
          </Select.Root>
          <div class="text-muted-foreground text-sm">
            How often you want to receive automation notifications
          </div>
        </div>
      </div>
    {/if}

    <div class="mt-6 border-t pt-6">
      <div class="mb-4 font-medium">Notification Channels</div>
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <div class="space-y-0.5">
            <div class="font-medium">Email Notifications</div>
            <div class="text-muted-foreground text-sm">Receive job alerts via email</div>
          </div>
          <Switch.Root
            checked={Boolean($form.jobEmailNotifications ?? true)}
            onCheckedChange={(checked) => {
              form.update((f) => ({ ...f, jobEmailNotifications: checked }));
              triggerFormChange();
            }} />
        </div>

        <div class="flex items-center justify-between">
          <div class="space-y-0.5">
            <div class="font-medium">Browser Notifications</div>
            <div class="text-muted-foreground text-sm">Receive job alerts in your browser</div>
          </div>
          <Switch.Root
            checked={Boolean($form.jobBrowserNotifications ?? true)}
            onCheckedChange={(checked) => {
              form.update((f) => ({ ...f, jobBrowserNotifications: checked }));
              triggerFormChange();
            }} />
        </div>
      </div>
    </div>
  </div>
</div>
