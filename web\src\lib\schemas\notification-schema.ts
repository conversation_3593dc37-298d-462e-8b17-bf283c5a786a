import { z } from 'zod';

/**
 * Email notification settings schema
 */
export const emailNotificationSchema = z.object({
  enabled: z.boolean().default(true),
  digest: z.enum(['daily', 'weekly', 'never']).default('daily'),
  format: z.enum(['html', 'text']).default('html'),
});

/**
 * Job notification settings schema
 */
export const jobNotificationSchema = z.object({
  matches: z.boolean().default(true),
  matchFrequency: z.enum(['realtime', 'daily', 'weekly']).default('daily'),
  applicationStatus: z.boolean().default(true),
  newJobs: z.boolean().default(true),
  newJobsFrequency: z.enum(['realtime', 'daily', 'weekly']).default('daily'),
  interviewReminders: z.boolean().default(true),
  savedJobsUpdates: z.boolean().default(true),
  emailNotifications: z.boolean().default(true),
  browserNotifications: z.boolean().default(true),
  mobileNotifications: z.boolean().default(false),
});

/**
 * Marketing notification settings schema
 */
export const marketingNotificationSchema = z.object({
  enabled: z.boolean().default(true),
  productUpdates: z.boolean().default(true),
  newsletterSubscription: z.boolean().default(false),
  eventInvitations: z.boolean().default(false),
});

/**
 * Platform notification settings schema
 */
export const platformNotificationSchema = z.object({
  browser: z.boolean().default(true),
  desktop: z.boolean().default(false),
  mobile: z.boolean().default(false),
  push: z.boolean().default(true),
});

/**
 * Complete notification settings schema
 */
export const notificationSettingsSchema = z.object({
  email: emailNotificationSchema,
  jobs: jobNotificationSchema,
  marketing: marketingNotificationSchema,
  platform: platformNotificationSchema,
});

/**
 * Database notification settings schema
 * This matches the Prisma model structure
 */
export const dbNotificationSettingsSchema = z.object({
  id: z.string().optional(),
  userId: z.string(),

  // Email notifications
  emailEnabled: z.boolean().default(true),
  emailDigest: z.string().default('daily'),
  emailFormat: z.string().default('html'),

  // Job notifications
  jobMatchEnabled: z.boolean().default(true),
  jobMatchFrequency: z.string().default('daily'),
  applicationStatusEnabled: z.boolean().default(true),
  newJobsEnabled: z.boolean().default(true),
  newJobsFrequency: z.string().default('daily'),
  interviewRemindersEnabled: z.boolean().default(true),
  savedJobsUpdatesEnabled: z.boolean().default(true),

  // Automation notifications
  automationEnabled: z.boolean().default(true),
  automationFrequency: z.string().default('realtime'),

  // Job notification channels
  jobEmailEnabled: z.boolean().default(true),
  jobBrowserEnabled: z.boolean().default(true),
  jobMobileEnabled: z.boolean().default(false),

  // Marketing notifications
  marketingEnabled: z.boolean().default(true),
  productUpdatesEnabled: z.boolean().default(true),
  newsletterEnabled: z.boolean().default(false),
  eventInvitationsEnabled: z.boolean().default(false),

  // Platform notifications
  browserEnabled: z.boolean().default(true),
  desktopEnabled: z.boolean().default(false),
  mobileEnabled: z.boolean().default(false),
  pushEnabled: z.boolean().default(true),

  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

/**
 * Form schema for notification settings page
 * This is a flattened version of the notification settings schema
 * for easier use with forms
 */
export const notificationFormSchema = z.object({
  // Email notifications
  emailNotifications: z.boolean().default(true),
  emailDigest: z.enum(['daily', 'weekly', 'never']).default('daily'),
  emailFormat: z.enum(['html', 'text']).default('html'),

  // Job notifications
  jobMatchNotifications: z.boolean().default(true),
  jobMatchFrequency: z.enum(['realtime', 'daily', 'weekly']).default('daily'),
  applicationStatusNotifications: z.boolean().default(true),
  newJobsNotifications: z.boolean().default(true),
  newJobsFrequency: z.enum(['realtime', 'daily', 'weekly']).default('daily'),
  interviewReminders: z.boolean().default(true),
  savedJobsUpdates: z.boolean().default(true),

  // Automation notifications
  automationNotifications: z.boolean().default(true),
  automationFrequency: z.enum(['realtime', 'daily', 'weekly']).default('realtime'),

  // Job notification channels
  jobEmailNotifications: z.boolean().default(true),
  jobBrowserNotifications: z.boolean().default(true),
  jobMobileNotifications: z.boolean().default(false),

  // Marketing notifications
  marketingEmails: z.boolean().default(true),
  productUpdates: z.boolean().default(true),
  newsletterSubscription: z.boolean().default(false),
  eventInvitations: z.boolean().default(false),

  // Platform notifications
  browserNotifications: z.boolean().default(true),
  desktopNotifications: z.boolean().default(false),
  mobileNotifications: z.boolean().default(false),
  pushNotifications: z.boolean().default(true),
});

/**
 * Helper function to convert form data to notification settings
 */
export function formToNotificationSettings(formData: z.infer<typeof notificationFormSchema>) {
  return {
    email: {
      enabled: formData.emailNotifications,
      digest: formData.emailDigest,
      format: formData.emailFormat,
    },
    jobs: {
      matches: formData.jobMatchNotifications,
      matchFrequency: formData.jobMatchFrequency,
      applicationStatus: formData.applicationStatusNotifications,
      newJobs: formData.newJobsNotifications,
      newJobsFrequency: formData.newJobsFrequency,
      interviewReminders: formData.interviewReminders,
      savedJobsUpdates: formData.savedJobsUpdates,
      emailNotifications: formData.jobEmailNotifications,
      browserNotifications: formData.jobBrowserNotifications,
      mobileNotifications: formData.jobMobileNotifications,
    },
    marketing: {
      enabled: formData.marketingEmails,
      productUpdates: formData.productUpdates,
      newsletterSubscription: formData.newsletterSubscription,
      eventInvitations: formData.eventInvitations,
    },
    platform: {
      browser: formData.browserNotifications,
      desktop: formData.desktopNotifications,
      mobile: formData.mobileNotifications,
      push: formData.pushNotifications,
    },
  };
}

/**
 * Helper function to convert notification settings to form data
 */
export function notificationSettingsToForm(settings: z.infer<typeof notificationSettingsSchema>) {
  return {
    emailNotifications: settings.email.enabled,
    emailDigest: settings.email.digest,
    emailFormat: settings.email.format,

    jobMatchNotifications: settings.jobs.matches,
    jobMatchFrequency: settings.jobs.matchFrequency,
    applicationStatusNotifications: settings.jobs.applicationStatus,
    newJobsNotifications: settings.jobs.newJobs,
    newJobsFrequency: settings.jobs.newJobsFrequency,
    interviewReminders: settings.jobs.interviewReminders,
    savedJobsUpdates: settings.jobs.savedJobsUpdates,

    jobEmailNotifications: settings.jobs.emailNotifications,
    jobBrowserNotifications: settings.jobs.browserNotifications,
    jobMobileNotifications: settings.jobs.mobileNotifications,

    marketingEmails: settings.marketing.enabled,
    productUpdates: settings.marketing.productUpdates,
    newsletterSubscription: settings.marketing.newsletterSubscription,
    eventInvitations: settings.marketing.eventInvitations,

    browserNotifications: settings.platform.browser,
    desktopNotifications: settings.platform.desktop,
    mobileNotifications: settings.platform.mobile,
    pushNotifications: settings.platform.push,
  };
}

/**
 * Helper function to convert form data to database model
 */
export function formToDbModel(
  formData: z.infer<typeof notificationFormSchema>,
  userId: string
): z.infer<typeof dbNotificationSettingsSchema> {
  return {
    userId,

    // Email notifications
    emailEnabled: formData.emailNotifications,
    emailDigest: formData.emailDigest,
    emailFormat: formData.emailFormat,

    // Job notifications
    jobMatchEnabled: formData.jobMatchNotifications,
    jobMatchFrequency: formData.jobMatchFrequency,
    applicationStatusEnabled: formData.applicationStatusNotifications,
    newJobsEnabled: formData.newJobsNotifications,
    newJobsFrequency: formData.newJobsFrequency,
    interviewRemindersEnabled: formData.interviewReminders,
    savedJobsUpdatesEnabled: formData.savedJobsUpdates,

    // Automation notifications
    automationEnabled: formData.automationNotifications,
    automationFrequency: formData.automationFrequency,

    // Job notification channels
    jobEmailEnabled: formData.jobEmailNotifications,
    jobBrowserEnabled: formData.jobBrowserNotifications,
    jobMobileEnabled: formData.jobMobileNotifications,

    // Marketing notifications
    marketingEnabled: formData.marketingEmails,
    productUpdatesEnabled: formData.productUpdates,
    newsletterEnabled: formData.newsletterSubscription,
    eventInvitationsEnabled: formData.eventInvitations,

    // Platform notifications
    browserEnabled: formData.browserNotifications,
    desktopEnabled: formData.desktopNotifications,
    mobileEnabled: formData.mobileNotifications,
    pushEnabled: formData.pushNotifications,
  };
}

/**
 * Helper function to convert database model to form data
 */
export function dbModelToForm(
  dbModel: z.infer<typeof dbNotificationSettingsSchema>
): z.infer<typeof notificationFormSchema> {
  return {
    // Email notifications
    emailNotifications: dbModel.emailEnabled,
    emailDigest: dbModel.emailDigest as 'daily' | 'weekly' | 'never',
    emailFormat: dbModel.emailFormat as 'html' | 'text',

    // Job notifications
    jobMatchNotifications: dbModel.jobMatchEnabled,
    jobMatchFrequency: dbModel.jobMatchFrequency as 'realtime' | 'daily' | 'weekly',
    applicationStatusNotifications: dbModel.applicationStatusEnabled,
    newJobsNotifications: dbModel.newJobsEnabled,
    newJobsFrequency: dbModel.newJobsFrequency as 'realtime' | 'daily' | 'weekly',
    interviewReminders: dbModel.interviewRemindersEnabled,
    savedJobsUpdates: dbModel.savedJobsUpdatesEnabled,

    // Automation notifications
    automationNotifications: dbModel.automationEnabled,
    automationFrequency: dbModel.automationFrequency as 'realtime' | 'daily' | 'weekly',

    // Job notification channels
    jobEmailNotifications: dbModel.jobEmailEnabled,
    jobBrowserNotifications: dbModel.jobBrowserEnabled,
    jobMobileNotifications: dbModel.jobMobileEnabled,

    // Marketing notifications
    marketingEmails: dbModel.marketingEnabled,
    productUpdates: dbModel.productUpdatesEnabled,
    newsletterSubscription: dbModel.newsletterEnabled,
    eventInvitations: dbModel.eventInvitationsEnabled,

    // Platform notifications
    browserNotifications: dbModel.browserEnabled,
    desktopNotifications: dbModel.desktopEnabled,
    mobileNotifications: dbModel.mobileEnabled,
    pushNotifications: dbModel.pushEnabled,
  };
}

export type NotificationSettings = z.infer<typeof notificationSettingsSchema>;
export type NotificationFormData = z.infer<typeof notificationFormSchema>;
