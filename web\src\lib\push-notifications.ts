import { browser } from '$app/environment';

// VAPID public key - this should match the server-side key
const VAPID_PUBLIC_KEY = 'BEl62iUYgUivxIkv69yViEuiBIa40HdHSWgNfZeeyCLdmw';

/**
 * Check if push notifications are supported
 */
export function isPushNotificationSupported(): boolean {
  if (!browser) return false;
  return 'serviceWorker' in navigator && 'PushManager' in window;
}

/**
 * Check current notification permission status
 */
export function getNotificationPermission(): NotificationPermission {
  if (!browser || !('Notification' in window)) return 'default';
  return Notification.permission;
}

/**
 * Request permission for push notifications and register service worker
 */
export async function requestPushNotificationPermission(): Promise<boolean> {
  if (!isPushNotificationSupported()) {
    console.warn('Push notifications are not supported in this browser');
    return false;
  }

  try {
    // Request notification permission
    const permission = await Notification.requestPermission();

    if (permission !== 'granted') {
      console.warn('Push notification permission denied');
      return false;
    }

    // Register service worker
    const registration = await navigator.serviceWorker.register('/sw.js');
    console.log('Service worker registered:', registration);

    // Subscribe to push notifications
    const subscription = await registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: urlBase64ToUint8Array(VAPID_PUBLIC_KEY),
    });

    // Send subscription to server
    const response = await fetch('/api/push/subscribe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(subscription),
    });

    if (!response.ok) {
      throw new Error('Failed to save push subscription');
    }

    console.log('Push notification subscription successful');
    return true;
  } catch (error) {
    console.error('Error setting up push notifications:', error);
    return false;
  }
}

/**
 * Unregister push notifications
 */
export async function unregisterPushNotifications(): Promise<void> {
  if (!isPushNotificationSupported()) return;

  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (registration) {
      const subscription = await registration.pushManager.getSubscription();
      if (subscription) {
        // Unsubscribe from push notifications
        await subscription.unsubscribe();

        // Notify server to remove subscription
        await fetch('/api/push/unsubscribe', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(subscription),
        });
      }
    }
    console.log('Push notifications unregistered');
  } catch (error) {
    console.error('Error unregistering push notifications:', error);
  }
}

/**
 * Check if user is currently subscribed to push notifications
 */
export async function isPushNotificationSubscribed(): Promise<boolean> {
  if (!isPushNotificationSupported()) return false;

  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (!registration) return false;

    const subscription = await registration.pushManager.getSubscription();
    return subscription !== null;
  } catch (error) {
    console.error('Error checking push subscription status:', error);
    return false;
  }
}

/**
 * Convert VAPID key from base64 to Uint8Array
 */
function urlBase64ToUint8Array(base64String: string): Uint8Array {
  const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
  const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/');
  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);
  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

/**
 * Show a test notification (for testing purposes)
 */
export async function showTestNotification(): Promise<void> {
  if (!isPushNotificationSupported()) return;

  if (getNotificationPermission() === 'granted') {
    new Notification('Test Notification', {
      body: 'This is a test notification from Auto Apply',
      icon: '/favicon.ico',
      badge: '/favicon.ico',
    });
  }
}
