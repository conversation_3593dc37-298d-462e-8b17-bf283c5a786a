<script lang="ts">
  interface Props {
    locationIds: string;
    fallback?: string;
  }

  let { locationIds, fallback = 'None specified' }: Props = $props();

  let resolvedLocations = $state('');
  let isLoading = $state(true);

  // Cache for resolved locations to avoid repeated API calls
  const locationCache = new Map<string, string>();

  async function resolveLocations() {
    if (!locationIds) {
      resolvedLocations = fallback;
      isLoading = false;
      return;
    }

    // Check cache first
    if (locationCache.has(locationIds)) {
      resolvedLocations = locationCache.get(locationIds)!;
      isLoading = false;
      return;
    }

    try {
      const locations = locationIds.split(', ').filter(Boolean);
      if (locations.length === 0) {
        resolvedLocations = fallback;
        isLoading = false;
        return;
      }

      const resolved = [];
      for (const location of locations) {
        if (location.includes('|')) {
          // Parse the location format: "id|name|state|country"
          const parts = location.split('|');
          if (parts.length >= 3) {
            resolved.push(`${parts[1]}, ${parts[2]}`);
          } else {
            resolved.push(location);
          }
        } else {
          // Try to resolve city ID
          try {
            const response = await fetch(`/api/locations/resolve`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ ids: [location] })
            });

            if (response.ok) {
              const cities = await response.json();
              if (cities.length > 0) {
                resolved.push(`${cities[0].name}, ${cities[0].state.code}`);
              } else {
                resolved.push(location);
              }
            } else {
              resolved.push(location);
            }
          } catch {
            resolved.push(location);
          }
        }
      }

      resolvedLocations = resolved.join(', ') || fallback;
      
      // Cache the result
      locationCache.set(locationIds, resolvedLocations);
    } catch (error) {
      console.error('Error resolving locations:', error);
      resolvedLocations = locationIds; // Fallback to IDs
    } finally {
      isLoading = false;
    }
  }

  // Resolve locations when component mounts or locationIds change
  $effect(() => {
    isLoading = true;
    resolveLocations();
  });
</script>

{#if isLoading}
  <span class="text-gray-400">Loading...</span>
{:else}
  <span>{resolvedLocations}</span>
{/if}
