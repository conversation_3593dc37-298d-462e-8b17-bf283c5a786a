import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function addPushSubscriptionsTable() {
  try {
    console.log('🚀 Starting database migration...');

    // First, check what tables exist
    const existingTables = await prisma.$queryRaw`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'web'
      ORDER BY table_name;
    `;

    console.log('📋 Existing tables:', existingTables);

    // Check if NotificationSettings table exists
    const notificationSettingsExists = await prisma.$queryRaw`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'web'
        AND table_name = 'NotificationSettings'
      );
    `;

    if (!(notificationSettingsExists as any)[0].exists) {
      console.log('➕ Creating NotificationSettings table...');

      await prisma.$executeRaw`
        CREATE TABLE "web"."NotificationSettings" (
          "id" TEXT NOT NULL,
          "userId" TEXT NOT NULL,
          "emailEnabled" BOOLEAN NOT NULL DEFAULT true,
          "emailDigest" TEXT NOT NULL DEFAULT 'daily',
          "emailFormat" TEXT NOT NULL DEFAULT 'html',
          "jobMatchEnabled" BOOLEAN NOT NULL DEFAULT true,
          "jobMatchFrequency" TEXT NOT NULL DEFAULT 'daily',
          "applicationStatusEnabled" BOOLEAN NOT NULL DEFAULT true,
          "newJobsEnabled" BOOLEAN NOT NULL DEFAULT true,
          "newJobsFrequency" TEXT NOT NULL DEFAULT 'daily',
          "interviewRemindersEnabled" BOOLEAN NOT NULL DEFAULT true,
          "savedJobsUpdatesEnabled" BOOLEAN NOT NULL DEFAULT true,
          "automationEnabled" BOOLEAN NOT NULL DEFAULT true,
          "automationFrequency" TEXT NOT NULL DEFAULT 'realtime',
          "jobEmailEnabled" BOOLEAN NOT NULL DEFAULT true,
          "jobBrowserEnabled" BOOLEAN NOT NULL DEFAULT true,
          "jobMobileEnabled" BOOLEAN NOT NULL DEFAULT false,
          "marketingEnabled" BOOLEAN NOT NULL DEFAULT true,
          "productUpdatesEnabled" BOOLEAN NOT NULL DEFAULT true,
          "newsletterEnabled" BOOLEAN NOT NULL DEFAULT false,
          "eventInvitationsEnabled" BOOLEAN NOT NULL DEFAULT false,
          "browserEnabled" BOOLEAN NOT NULL DEFAULT true,
          "desktopEnabled" BOOLEAN NOT NULL DEFAULT false,
          "mobileEnabled" BOOLEAN NOT NULL DEFAULT false,
          "pushEnabled" BOOLEAN NOT NULL DEFAULT true,
          "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "updatedAt" TIMESTAMP(3) NOT NULL,

          CONSTRAINT "NotificationSettings_pkey" PRIMARY KEY ("id")
        );
      `;

      // Add unique constraint and foreign key
      await prisma.$executeRaw`
        CREATE UNIQUE INDEX "NotificationSettings_userId_key" ON "web"."NotificationSettings"("userId");
      `;

      await prisma.$executeRaw`
        ALTER TABLE "web"."NotificationSettings" ADD CONSTRAINT "NotificationSettings_userId_fkey" FOREIGN KEY ("userId") REFERENCES "web"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
      `;

      console.log('✅ NotificationSettings table created successfully');
    } else {
      console.log('✅ NotificationSettings table already exists');

      // Check if automation fields exist
      const automationColumnExists = await prisma.$queryRaw`
        SELECT EXISTS (
          SELECT FROM information_schema.columns
          WHERE table_schema = 'web'
          AND table_name = 'NotificationSettings'
          AND column_name = 'automationEnabled'
        );
      `;

      if (!(automationColumnExists as any)[0].exists) {
        console.log('➕ Adding automation fields to NotificationSettings...');

        await prisma.$executeRaw`
          ALTER TABLE "web"."NotificationSettings"
          ADD COLUMN "automationEnabled" BOOLEAN NOT NULL DEFAULT true,
          ADD COLUMN "automationFrequency" TEXT NOT NULL DEFAULT 'realtime';
        `;

        console.log('✅ Automation fields added to NotificationSettings');
      } else {
        console.log('✅ Automation fields already exist in NotificationSettings');
      }
    }

    // Check if PushSubscription table exists
    const pushTableExists = await prisma.$queryRaw`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'web'
        AND table_name = 'PushSubscription'
      );
    `;

    if (!(pushTableExists as any)[0].exists) {
      console.log('➕ Creating PushSubscription table...');

      await prisma.$executeRaw`
        CREATE TABLE "web"."PushSubscription" (
          "id" TEXT NOT NULL,
          "userId" TEXT NOT NULL,
          "endpoint" TEXT NOT NULL,
          "p256dh" TEXT,
          "auth" TEXT,
          "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "updatedAt" TIMESTAMP(3) NOT NULL,

          CONSTRAINT "PushSubscription_pkey" PRIMARY KEY ("id")
        );
      `;

      // Add unique constraint
      await prisma.$executeRaw`
        CREATE UNIQUE INDEX "PushSubscription_userId_endpoint_key" ON "web"."PushSubscription"("userId", "endpoint");
      `;

      // Add foreign key constraint
      await prisma.$executeRaw`
        ALTER TABLE "web"."PushSubscription" ADD CONSTRAINT "PushSubscription_userId_fkey" FOREIGN KEY ("userId") REFERENCES "web"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
      `;

      console.log('✅ PushSubscription table created successfully');
    } else {
      console.log('✅ PushSubscription table already exists');
    }
  } catch (error) {
    console.error('❌ Error during migration:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
addPushSubscriptionsTable()
  .then(() => {
    console.log('🎉 Migration completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  });
