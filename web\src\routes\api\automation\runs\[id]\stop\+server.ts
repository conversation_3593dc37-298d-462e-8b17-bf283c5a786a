import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { getUserFromToken } from '$lib/server/auth.js';
import type { RequestHandler } from './$types';
import { createClient } from 'redis';

// Using the shared Prisma client from $lib/server/prisma

// Stop an automation run
export const POST: RequestHandler = async ({ params, cookies }) => {
  const user = getUserFromToken(cookies);
  const { id } = params;

  if (!user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Check if the automation run exists and belongs to the user
    const automationRun = await prisma.automationRun.findFirst({
      where: {
        id,
        OR: [
          { userId: user.id },
          {
            profile: {
              team: {
                members: {
                  some: { userId: user.id },
                },
              },
            },
          },
        ],
      },
    });

    if (!automationRun) {
      return json({ error: 'Automation run not found' }, { status: 404 });
    }

    // If the run is already stopped, completed, or failed, return success
    if (['stopped', 'completed', 'failed'].includes(automationRun.status)) {
      return json({ message: 'Automation run already stopped' });
    }

    // Send a message to Redis or the mock worker to stop the automation
    try {
      // Try to use Redis first
      try {
        const redisUrl =
          process.env.NODE_ENV === 'production'
            ? 'rediss://red-cvmu1me3jp1c738ve7ig:<EMAIL>:6379'
            : 'redis://localhost:6379';

        const redis = createClient({
          url: redisUrl,
        });

        await redis.connect();

        // Send a message to the automation worker to stop the run
        await redis.publish(
          'automation:stop',
          JSON.stringify({
            runId: automationRun.id,
            userId: user.id,
          })
        );

        await redis.disconnect();
        console.log('Sent stop request to Redis');
      } catch (redisError) {
        console.error('Redis error, falling back to mock worker:', redisError);

        // Fall back to the mock worker
        try {
          const response = await fetch('http://localhost:3001/stop', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              runId: automationRun.id,
              userId: user.id,
            }),
          });

          if (!response.ok) {
            throw new Error(`Mock worker returned ${response.status}: ${response.statusText}`);
          }

          console.log('Sent stop request to mock worker');
        } catch (mockError) {
          console.error('Mock worker error:', mockError);
          // Continue even if the mock worker fails
        }
      }
    } catch (error) {
      console.error('Error sending stop request:', error);
      // If both Redis and mock worker fail, we still update the database
    }

    // Update the run status to 'stopped'
    const updatedRun = await prisma.automationRun.update({
      where: { id },
      data: {
        status: 'stopped',
        stoppedAt: new Date(),
      },
    });

    // Send notification using the same approach as resume parsing
    try {
      const { createNotification } = await import('$lib/server/notifications');
      await createNotification({
        userId: user.id,
        title: 'Automation Run Stopped',
        message: `Your automation run was stopped manually.`,
        url: `/dashboard/automation/${id}`,
        type: 'warning',
        data: {
          automationRunId: id,
          status: 'stopped',
        },
      });
      console.log(`[automation] Sent stop notification to user ${user.id}`);
    } catch (notificationError) {
      console.error('Error sending stop notification:', notificationError);
      // Don't fail the request if notification fails
    }

    return json(updatedRun);
  } catch (error) {
    console.error('Error stopping automation run:', error);
    return json({ error: 'Failed to stop automation run' }, { status: 500 });
  }
};
